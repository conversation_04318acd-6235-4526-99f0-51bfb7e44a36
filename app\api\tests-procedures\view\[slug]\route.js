import { query } from '@/app/lib/db';
import sectionMappings from '../../sectionMappings';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

// Fields that should be rendered as markdown
const markdownFields = ['overview', 'why_its_done', 'risks', 'how_to_prepare', 'what_to_expect', 'results'];

export async function GET(request, { params }) {
  try {
    const { slug } = params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';

    const languageId = languageMap[lang] || 1;
    

    let result;

    if(languageId){
           result = await query(
             `SELECT
                    tp.id,
                    COALESCE(tpt.name, tp.name) AS name,
                    tp.slug,
                     tp.overview as tp_overview,
                    tp.why_its_done as tp_why_its_done,
                    tp.risks as tp_risks,
                    tp.how_to_prepare as tp_how_to_prepare,
                    tp.what_to_expect as tp_what_to_expect,
                    tp.results as tp_results,
                    tp.body_html as tp_body_html,
                    tpt.overview AS overview,
                    tpt.why_its_done AS why_its_done,
                    tpt.risks AS risks,
                    tpt.how_to_prepare AS how_to_prepare,
                    tpt.what_to_expect AS what_to_expect,
                    tpt.results AS results,
                    tpt.body_html AS body_html,
                    ttpt.title AS meta_title, 
                    ttpt.description AS meta_description,
                  tp.created_at
                FROM
                    test_procedures_new tp
                LEFT JOIN
                    test_procedures_translations_new tpt ON tp.id = tpt.procedure_id AND tpt.language_id = $2
                LEFT JOIN 
                    procedures_meta_tags ttpt ON tp.id = ttpt.procedure_id AND ttpt.language_id = $2
                WHERE
                    tp.slug = $1
                `,
             [slug, languageId]
           );
    } else {
         result = await query(
           `SELECT
                    tp.id,
                    tp.name,
                    tp.slug,
                    tp.overview,
                    tp.why_its_done,
                    tp.risks,
                    tp.how_to_prepare,
                    tp.what_to_expect,
                    tp.results,
                    tp.body_html,
                    ttpt.title AS meta_title, 
                    ttpt.description AS meta_description,
                   tp.created_at
                FROM
                    test_procedures_new tp
                LEFT JOIN 
                    procedures_meta_tags ttpt ON tp.id = ttpt.procedure_id AND ttpt.language_code = 'en'
                WHERE
                    tp.slug = $1
              `,
           [slug]
         );

    }


    if (result.rows.length === 0) {
      return Response.json(
        { error: 'Test or procedure not found' },
        { status: 404 }
      );
    }

    const test = result.rows[0];
       // Transform the data to include sections for markdown content
    const sections = markdownFields
        .filter(field => {
            const translatedContent = test[field]
            const englishContent = test[`tp_${field}`]
            return (translatedContent || englishContent) && (translatedContent !== 'NaN' && translatedContent !== null && translatedContent !== undefined ) || (englishContent !== 'NaN' && englishContent !== null && englishContent !== undefined)
        })
        .map(field => ({
            heading: sectionMappings[lang][field],
            content: test[field] || test[`tp_${field}`]
        }));


    // Construct the response
    const response = {
      id: test.id,
      name: test.name,
      slug: test.slug,
      sections: sections, 
      body_html: test.body_html,
      meta: {
        created_at: test.created_at
      }
    };

    return Response.json(response);
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load test details' },
      { status: 500 }
    );
  }
}