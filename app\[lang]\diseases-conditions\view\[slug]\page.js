import DetailViewClient from '../../../../components/shared/DetailViewClient';
import { getConditionMetadata } from '../../../../api/diseases-conditions/meta/getMetaData';
import translationStrings from '../../language/translations';
const logger = require('../../../../utils/logger');

async function fetchCondition(slug, lang) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/diseases-conditions/view/${slug}?lang=${lang}`, {
      method: 'GET',
      headers: {
        'Accept-Language': lang,
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error('Failed to fetch condition');
    }
    return response.json();
  } catch (error) {
    logger.error('Error fetching condition:', error);
    throw error;
  }
}

export default async function ConditionPage({ params }) {
  const { slug, lang } = params;
  const language = lang || 'en';
  
  // Create a safe version of langStrings without functions
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    // Remove the function property
    noConditionsFound: undefined
  };

  try {
    // Fetch condition data server-side
    const condition = await fetchCondition(slug, language);
    const { name, meta_title, meta_description } = condition;

    const metaTitle = meta_title || name || 'Default Title';
    const metaDescription = meta_description || name || 'Default Description';

    const breadcrumbItems = [
      { text: langStrings.home, href: `/${language}` },
      { text: langStrings.title, href: `/${language}/diseases-conditions` },
      { text: condition?.name || '' }
    ];

    return (
      <DetailViewClient
        condition={condition}
        breadcrumbItems={breadcrumbItems}
        metaTitle={metaTitle}
        metaDescription={metaDescription}
        language={language}
        langStrings={langStrings}
      />
    );
  } catch (error) {
    logger.error('Error in ConditionPage:', error);
    return (
      <DetailViewClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        breadcrumbItems={[
          { text: langStrings.home, href: `/${language}` },
          { text: langStrings.title, href: `/${language}/diseases-conditions` }
        ]}
      />
    );
  }
}