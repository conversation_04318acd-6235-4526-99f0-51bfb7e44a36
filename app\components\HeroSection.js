'use client';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import SearchBar from './SearchBar';
import AlphabetGrid from './AlphabetGrid';

export default function HeroSection({ 
  title, 
  description, 
  searchPlaceholder,
  indices,
  browseByLetterText,
  baseUrl,
  tags
}) {
  return (
    <Box 
      sx={{ 
        backgroundColor: '#F7F6F4',
        py: { xs: 4, sm: 5, md: 6 },
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={{ xs: 3, sm: 4, md: 6 }} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography 
              variant="h3" 
              component="h1" 
              gutterBottom
              sx={{ 
                fontWeight: 'bold',
                mb: { xs: 2, sm: 3 },
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
              }}
            >
              {title}
            </Typography>
            <Typography 
              variant="h6" 
              sx={{ 
                color: 'text.secondary',
                mb: { xs: 3, sm: 4 },
                fontSize: { xs: '1rem', sm: '1.15rem', md: '1.25rem' }
              }}
            >
              {description}
            </Typography>
            <SearchBar placeholder={searchPlaceholder} indices={indices} tags={tags} />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ 
              backgroundColor: 'white',
              p: { xs: 2, sm: 3 },
              borderRadius: 2,
              boxShadow: 0
            }}>
              <Typography 
                variant="h6" 
                sx={{ 
                  mb: { xs: 1.5, sm: 2 },
                  fontSize: { xs: '1rem', sm: '1.25rem' }
                }}
              >
                {browseByLetterText}
              </Typography>
              <AlphabetGrid baseUrl={baseUrl} />
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}
