'use client';

import NavBar from '../../../../components/NavBar';
import ContentSection from '../../../../components/ContentSection';
import ShareButton from '../../../../components/shared/ShareButton';
import { Box, Container, Typography, Breadcrumbs, Link as MuiLink } from '@mui/material';
import Link from 'next/link';
import Footer from '../../../../components/Footer';
import { Helmet } from 'react-helmet-async';
import { getRedirectPath } from '@/app/utils/getRedirectPath';
import Widget from '@/app/components/Widget';

export default function SymptomViewClient({
  symptom,
  language,
  langStrings,
  metaTitle,
  metaDescription,
  error
}) {
  if (error || !symptom) {
    return (
      <div>
        <NavBar />
        <Box sx={{ backgroundColor: '#F7F6F4', py: 4 }}>
          <Container>
            <Typography variant="h4">
              {error || langStrings.symptomNotFound}
            </Typography>
          </Container>
        </Box>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Helmet>
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />

      {/* Header Section */}
      <Box sx={{ backgroundColor: "#F7F6F4", py: 6 }}>
        <Container maxWidth="lg">
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link
              href={getRedirectPath(`/${language}`)}
              passHref
              style={{ textDecoration: "none" }}
            >
              <MuiLink color="inherit" underline="hover">
                {langStrings.home}
              </MuiLink>
            </Link>
            <Link
              href={getRedirectPath(`/${language}/symptoms`)}
              passHref
              style={{ textDecoration: "none" }}
            >
              <MuiLink color="inherit" underline="hover">
                {langStrings.title}
              </MuiLink>
            </Link>
            <Typography color="text.primary">{symptom.name}</Typography>
          </Breadcrumbs>

          {/* Title and Description */}
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{ fontWeight: "bold", mb: 2 }}
          >
            {symptom.name}
          </Typography>
          {symptom.definition && (
            <Typography
              variant="h6"
              sx={{
                color: "text.secondary",
                mb: 2,
                maxWidth: "800px",
              }}
            >
              {symptom.definition}
            </Typography>
          )}

          {/* Share Button */}
          <Box sx={{ mb: 4 }}>
            <ShareButton copiedText={"Link copied to clipboard"} />
          </Box>
        </Container>
      </Box>

      {/* Main Content */}
      <Box sx={{ pb: 6, pt: 3 }}>
        <Container maxWidth="lg">
          <Widget />
          <Box sx={{ maxWidth: "800px" }}>
            <ContentSection sections={symptom.sections} />
            {symptom.url && (
              <Box
                sx={{
                  mt: 4,
                  pt: 4,
                  borderTop: "1px solid",
                  borderColor: "divider",
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {langStrings.learnMore}
                  <Link
                    href={symptom.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {symptom.url}
                  </Link>
                </Typography>
              </Box>
            )}
          </Box>
        </Container>
      </Box>
      <Widget
        isAtBottom={true}
        title="Want a 1:1 answer for your situation?"
        description="Ask your question privately on August, your 24/7 personal AI health assistant."
      />
      <Footer />
    </div>
  );
}
