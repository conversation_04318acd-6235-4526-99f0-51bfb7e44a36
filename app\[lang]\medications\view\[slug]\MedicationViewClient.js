'use client';

import NavBar from '../../../../components/NavBar';
import ContentSection from '../../../../components/ContentSection';
import ShareButton from '../../../../components/shared/ShareButton';
import { Box, Container, Typography, Breadcrumbs, Link as MuiLink } from '@mui/material';
import Link from 'next/link';
import Footer from '../../../../components/Footer';
import { Helmet } from 'react-helmet-async';
import { getRedirectPath } from '@/app/utils/getRedirectPath';
import Widget from '@/app/components/Widget';

export default function MedicationViewClient({
  medication,
  language,
  langStrings,
  metaTitle,
  metaDescription,
  error
}) {
  if (error || !medication) {
    return (
      <div>
        <NavBar />
        <Box sx={{ backgroundColor: '#F7F6F4', py: 4 }}>
          <Container>
            <Typography variant="h4">
              {error || langStrings.noMedicationsFound}
            </Typography>
          </Container>
        </Box>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Helmet>
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />

      {/* Header Section */}
      <Box sx={{ backgroundColor: "#F7F6F4", py: 6 }}>
        <Container maxWidth="lg">
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link
              href={getRedirectPath(`/${language}`)}
              passHref
              style={{ textDecoration: "none" }}
            >
              <MuiLink color="inherit" underline="hover">
                {langStrings.home}
              </MuiLink>
            </Link>
            <Link
              href={getRedirectPath(`/${language}/medications`)}
              passHref
              style={{ textDecoration: "none" }}
            >
              <MuiLink color="inherit" underline="hover">
                {langStrings.medicationsTitle}
              </MuiLink>
            </Link>
            <Typography color="text.primary">{medication.name}</Typography>
          </Breadcrumbs>

          {/* Title and Type */}
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: "bold",
              mb: 2,
              wordWrap: "break-word",
            }}
          >
            {medication.name}
          </Typography>
          {medication.description && (
            <Typography
              variant="h6"
              sx={{
                color: "text.secondary",
                mb: 2,
                maxWidth: "800px",
              }}
            >
              {medication.description}
            </Typography>
          )}

          {/* Share Button */}
          <Box sx={{ mb: 4 }}>
            <ShareButton
              shareText={langStrings.share}
              copiedText={"Link copied to clipboard"}
            />
          </Box>
        </Container>
      </Box>

      {/* Main Content */}
      <Box sx={{ pb: 6, pt: 3 }}>
        <Container maxWidth="lg">
          <Widget />
          <Box sx={{ maxWidth: "800px" }}>
            <ContentSection sections={medication.sections} />
          </Box>
        </Container>
      </Box>
      <div className=" mx-auto ">
        <Widget
          isAtBottom={true}
          title="Want a 1:1 answer for your situation?"
          description="Ask your question privately on August, your 24/7 personal AI health assistant."
        />
      </div>
      <Footer />
    </div>
  );
}
