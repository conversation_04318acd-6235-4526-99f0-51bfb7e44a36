/* Box component */
.box {
  box-sizing: border-box;
}

/* Container components */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 16px;
  padding-right: 16px;
}

.containerLg {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 16px;
  padding-right: 16px;
  max-width: 1200px;
}

@media (min-width: 600px) {
  .container,
  .containerLg {
    padding-left: 24px;
    padding-right: 24px;
  }
}

/* Grid system */
.grid {
  box-sizing: border-box;
}

.gridContainer {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.gridItem {
  margin: 0;
  box-sizing: border-box;
}

/* Grid spacing */
.spacing4 {
  margin: -16px;
}

.spacing4 > .gridItem {
  padding: 16px;
}

.spacing6 {
  margin: -24px;
}

.spacing6 > .gridItem {
  padding: 24px;
}

/* Grid breakpoints */
.xs12 {
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
}

.xs6 {
  flex-grow: 0;
  max-width: 50%;
  flex-basis: 50%;
}

.xs3 {
  flex-grow: 0;
  max-width: 25%;
  flex-basis: 25%;
}

@media (min-width: 600px) {
  .sm6 {
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
  }
}

@media (min-width: 960px) {
  .md6 {
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
  }
  
  .md3 {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
  }
}

/* Typography variants */
.h1 {
  font-family: inherit;
  font-weight: 300;
  font-size: 6rem;
  line-height: 1.167;
  letter-spacing: -0.01562em;
  margin: 0;
}

.h2 {
  font-family: inherit;
  font-weight: 300;
  font-size: 3.75rem;
  line-height: 1.2;
  letter-spacing: -0.00833em;
  margin: 0;
}

.h3 {
  font-family: inherit;
  font-weight: 400;
  font-size: 3rem;
  line-height: 1.167;
  letter-spacing: 0em;
  margin: 0;
}

.h4 {
  font-family: inherit;
  font-weight: 400;
  font-size: 2.125rem;
  line-height: 1.235;
  letter-spacing: 0.00735em;
  margin: 0;
}

.h5 {
  font-family: inherit;
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 1.334;
  letter-spacing: 0em;
  margin: 0;
}

.h6 {
  font-family: inherit;
  font-weight: 500;
  font-size: 1.25rem;
  line-height: 1.6;
  letter-spacing: 0.0075em;
  margin: 0;
}

.subtitle1 {
  font-family: inherit;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.75;
  letter-spacing: 0.00938em;
  margin: 0;
}

.body1 {
  font-family: inherit;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  margin: 0;
}

.body2 {
  font-family: inherit;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  margin: 0;
}

/* Utility classes */
.heroSection {
  background-color: #F7F6F4;
  padding: 48px 0;
}

.section {
  padding: 64px 0;
}

.flexBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.textBold {
  font-weight: bold;
}

.textSecondary {
  color: #666;
}

.textPrimary {
  color: #206E55;
}

.mb4 {
  margin-bottom: 32px;
}

.mb3 {
  margin-bottom: 24px;
}

.mb2 {
  margin-bottom: 16px;
}

.mb100 {
  margin-bottom: 100px;
}

.wordWrap {
  word-wrap: break-word;
}

.hoverUnderline:hover {
  text-decoration: underline;
}
