import { query } from '@/app/lib/db';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

export const revalidate = 3600; // Cache for 1 hour

export async function GET(request, { params }) {
  // Enable edge caching
  const cacheControl = request.headers.get('cache-control') || '';
  if (!cacheControl.includes('no-cache')) {
    // Cache the response for 1 hour on the edge
    request.headers.set('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate=86400');
  }

  try {
    const { letter } = await params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const offset = (page - 1) * limit;
        
    const languageId = languageMap[lang] || 1;
    
    let result;
    if (languageId != 1) {
      // Combine count and data in a single query using WITH clause
      result = await query(
        `WITH data AS (
          SELECT tp.id, 
            COALESCE(tpt.name, tp.name) AS name, 
            tp.slug, 
            ttpt.title AS page_title, 
            ttpt.description AS page_description, 
            ttpt.indextitle AS page_indextitle,
            COUNT(*) OVER() as total_count
          FROM test_procedures tp
          LEFT JOIN test_procedures_translations tpt ON tp.id = tpt.procedure_id AND tpt.language_id = $2
          LEFT JOIN procedures_page_meta_tags ttpt ON tpt.language_id = ttpt.language_id
          WHERE tpt.first_letter = UPPER($1)
          ORDER BY COALESCE(tpt.name, tp.name) ASC
          LIMIT $3 OFFSET $4
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, languageId, limit, offset]
      );
    } else {
      // Combine count and data in a single query using WITH clause
      result = await query(
        `WITH data AS (
          SELECT tp.id, 
            tp.name, 
            tp.slug, 
            ttpt.title AS page_title, 
            ttpt.description AS page_description, 
            ttpt.indextitle AS page_indextitle,
            COUNT(*) OVER() as total_count
          FROM test_procedures tp
          LEFT JOIN procedures_page_meta_tags ttpt ON ttpt.language_id = 1
          WHERE tp.first_letter = UPPER($1)
          ORDER BY tp.name ASC
          LIMIT $2 OFFSET $3
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, limit, offset]
      );
    }
    const totalCount = result.rows[0]?.total || 0;
    
    return new Response(JSON.stringify({ 
      items: result.rows,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400'
      }
    });
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load tests and procedures' },
      { status: 500 }
    );
  }
}
