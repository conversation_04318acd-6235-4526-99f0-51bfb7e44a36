'use client';
import { Box, Container, Typography, Alert } from '@mui/material';
import NavBar from '../NavBar';
import { useLanguage } from '../../contexts/LanguageContext';
import Footer from '../Footer';

export default function ErrorView({ message }) {
  const { language } = useLanguage();

  return (
    <div>
      <NavBar />
      <Box sx={{ py: 4 }}>
        <Container>
          <Alert severity="error" sx={{ mb: 2 }}>
            {message || (language === 'es' ? 'Contenido no encontrado' : (language === 'fr' ? 'Contenu non trouvé' : 'Content not found'))}
          </Alert>
        </Container>
      </Box>
      <Footer />
    </div>
  );
}
