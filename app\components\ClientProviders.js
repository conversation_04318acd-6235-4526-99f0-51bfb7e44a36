'use client';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { createTheme } from '@mui/material/styles';
import { HelmetProvider } from 'react-helmet-async';
import { LanguageStringsProvider } from '../contexts/LanguageStringsContext';
import { LanguageProvider } from '../contexts/LanguageContext';
import { useParams } from 'next/navigation';

// Optimized theme with minimal configuration but preserving original styling
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#206E55',
    },
    secondary: {
      main: '#9c27b0',
    },
    background: {
      default: '#fff',
      paper: '#fff',
    }
  },
  typography: {
    fontFamily: 'inherit', // Use inherited font from layout
  },
  // Minimal CSS baseline to reduce initial styles but preserve functionality
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          margin: 0,
          padding: 0,
        },
      },
    },
  },
});

export default function ClientProviders({ children }) {
  const params = useParams();
  const language = params?.lang || 'en';

  return (
    <HelmetProvider>
      <LanguageProvider initialLanguage={language}>
        <LanguageStringsProvider>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            {children}
          </ThemeProvider>
        </LanguageStringsProvider>
      </LanguageProvider>
    </HelmetProvider>
  );
}
