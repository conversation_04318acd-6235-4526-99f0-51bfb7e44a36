{"lang": "pt", "title": "Calculadora de Necessidades Calóricas Diárias - Calculadora Precisa de BMR & TDEE | MeetAugust", "meta_description": "Calcule suas necessidades calóricas diárias com nossa avançada calculadora de BMR. Obtenha metas calóricas personalizadas para perda de peso, manutenção e ganho muscular. Grátis, precisa e baseada na ciência.", "meta_keywords": "calculadora de calorias, calculadora BMR, calculadora TDEE, necessidades calóricas diárias, calculadora de perda de peso, calculadora de metabolismo, planejamento nutricional", "og_title": "Calculadora de Necessidades Calóricas Diárias - Calculadora Precisa de BMR & TDEE", "og_description": "Calcule suas necessidades calóricas diárias com nossa avançada calculadora de BMR. Obtenha metas calóricas personalizadas para perda de peso, manutenção e ganho muscular.", "twitter_title": "Calculadora de Necessidades Calóricas Diárias - Calculadora Precisa de BMR & TDEE", "twitter_description": "Calcule suas necessidades calóricas diárias com nossa avançada calculadora de BMR. Obtenha metas calóricas personalizadas para perda de peso, manutenção e ganho muscular.", "canonical_url": "https://www.meetaugust.ai/calculator/calorie", "logo_alt": "Logo da Calculadora", "talk_to_august_url": "https://app.meetaugust.ai/redirect/wa?message=Hello%20August", "talk_to_august": "Fale com August", "main_title": "Calculadora Avançada de Necessidades Calóricas Diárias", "main_description": "Descubra quantas calorias você precisa diariamente com nossa calculadora precisa de calorias. Perfeita para perda de peso, ganho muscular, <br /> ou manter um estilo de vida saudável com base no seu corpo, objetivos e nível de atividade.", "us_units": "Unidades dos EUA", "metric_units": "Unidades Métricas", "form_instruction": "Insira seus dados pessoais abaixo e clique em Calcular para obter suas recomendações calóricas personalizadas", "age_label": "<PERSON><PERSON>", "age_range": "idades 15 - 80", "gender_label": "<PERSON><PERSON><PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino", "height_label": "Altura", "feet": "pés", "inches": "polegadas", "cm": "cm", "weight_label": "Peso", "pounds": "libras", "kg": "kg", "activity_label": "Atividade", "sedentary_option": "Sedentário: pouco ou nenhum exerc<PERSON>cio", "light_option": "Levemente ativo: exercício leve 1-3 dias/semana", "moderate_option": "Moderadamente ativo: exer<PERSON><PERSON><PERSON> moderado 3-5 dias/semana", "very_active_option": "<PERSON><PERSON> ativo: exer<PERSON><PERSON><PERSON> intenso 6-7 dias/semana", "super_active_option": "Super ativo: exer<PERSON><PERSON><PERSON> muito intenso, trabalho físico", "settings_link": "+ Configuraç<PERSON>es", "settings_header": "Configurações", "results_unit_label": "Unidade dos resultados:", "calories_unit": "Caloria<PERSON>", "kilojoules_unit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body_fat_percentage_label": "Percentual <PERSON> Corporal:", "body_fat_percentage_info": "Insira seu percentual de gordura corporal para cálculos mais precisos de composição corporal", "bmr_estimation_formula_label": "Fórmula de estimativa de BMR:", "bmr_estimation_formula_info": "Escolha a fórmula para calcular sua Taxa Metabólica Basal", "mifflin_formula": "Mifflin St Jeor", "harris_formula": "<PERSON><PERSON><PERSON>", "katch_formula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calculate_button": "Calcular ▶", "clear_button": "Limpar", "results_header": "<PERSON><PERSON><PERSON><PERSON>", "download_results_pdf": "Baixar resultados em PDF", "download_pdf": "Baixar PDF", "results_description": "Suas metas calóricas personalizadas são calculadas usando fórmulas metabólicas avançadas. Essas recomendações fornecem diretrizes de ingestão calórica diária adaptadas aos seus objetivos específicos - seja para manter seu peso atual, alcançar perda de peso sustentável ou apoiar ganho de peso saudável.", "bmr_label": "Taxa Metabólica Basal (BMR):", "calories_per_day": "calorias/dia", "activity_levels_label": "Níveis de Atividade:", "sedentary_label": "<PERSON><PERSON><PERSON><PERSON>", "sedentary_description": "pouco ou nenhum exerc<PERSON>cio", "light_label": "<PERSON><PERSON>", "light_description": "exercício 1-3 vezes/semana", "moderate_label": "Moderado", "moderate_description": "exercício 4-5 vezes/semana", "active_label": "Ativo", "active_description": "exer<PERSON><PERSON><PERSON> ou intenso 3-4 vezes/semana", "very_active_label": "<PERSON><PERSON>", "very_active_description": "exer<PERSON><PERSON><PERSON> intenso 6-7 vezes/semana", "extra_active_label": "Extra Ativo", "extra_active_description": "exercício muito intenso diariamente, ou trabalho físico", "goal_label": "Objetivo", "calories_label": "Caloria<PERSON>", "daily_calories_label": "<PERSON><PERSON><PERSON>", "maintain_weight": "Manter peso", "mild_weight_loss": "Perda de peso leve", "mild_weight_loss_rate": "0,5 lb/semana", "weight_loss": "Perda de peso", "weight_loss_rate": "1 lb/semana", "extreme_weight_loss": "Perda de peso extrema", "extreme_weight_loss_rate": "2 lb/semana", "show_weight_gain_info": "Mostrar informações para ganho de peso", "weight_gain_info": "Informações de Ganho de Peso", "mild_weight_gain": "Ganho de peso leve", "mild_weight_gain_rate": "0,25 kg/semana", "weight_gain": "Ganho de peso", "weight_gain_rate": "0,5 kg/semana", "fast_weight_gain": "Ganho de peso rápido", "fast_weight_gain_rate": "1 kg/semana", "physical_activity_guidelines": "Diretrizes de Atividade Física", "light_exercise_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light_exercise_description": "20-40 minutos de atividades de intensidade moderada como caminhada ou ioga leve.", "moderate_exercise_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moderate_exercise_description": "30-60 minutos de atividades que aumentam sua frequência cardíaca, como caminhada rápida ou ciclismo.", "vigorous_exercise_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vigorous_exercise_description": "45-90 minutos de treino de alta intensidade, esportes ou atividades físicas exigentes.", "professional_training_label": "Treinamento Profissional/Atlético", "professional_training_description": "2+ horas de treinamento intensivo ou trabalho ocupacional fisicamente exigente.", "nutritional_reference_guide": "Guia Completo de Referência Nutricional", "nutritional_reference_description": "Use estas tabelas abrangentes para tomar decisões dietéticas informadas e entender melhor o conteúdo calórico de alimentos do dia a dia, estratégias de planejamento de refeições e gasto energético do exercício.", "caloric_content_popular_foods": "<PERSON><PERSON><PERSON><PERSON> de Alimentos Populares", "food_item_label": "Alimento", "portion_size_label": "<PERSON><PERSON><PERSON> da Porção", "kj_label": "kJ", "fresh_fruits_label": "<PERSON><PERSON><PERSON>", "mango": "Manga", "mango_portion": "1 média (5 oz.)", "kiwi": "<PERSON><PERSON>", "kiwi_portion": "1 grande (3 oz.)", "blueberries": "<PERSON><PERSON><PERSON>", "blueberries_portion": "1 xícara", "avocado": "Abacate", "avocado_portion": "1/2 média (3 oz.)", "cherries": "<PERSON><PERSON><PERSON>", "cherries_portion": "1 xícara", "fresh_vegetables_label": "<PERSON><PERSON><PERSON><PERSON>", "sweet_potato": "Batata-doce", "sweet_potato_portion": "1 média (5 oz.)", "bell_pepper": "Pimentão", "bell_pepper_portion": "1 xícara fatiada", "spinach": "Espinafre", "spinach_portion": "2 xícaras frescas", "zucchini": "Abobrinha", "zucchini_portion": "1 xícara fatiada", "cauliflower": "Couve-flor", "cauliflower_portion": "1 xícara", "protein_sources_label": "Fontes de Proteína", "salmon_grilled": "Salmão, grelhado", "salmon_portion": "3 oz.", "turkey_breast": "Peito de peru", "turkey_portion": "3 oz.", "greek_yogurt": "<PERSON><PERSON><PERSON><PERSON> grego", "greek_yogurt_portion": "Pote de 6 oz.", "almonds": "Amêndoas", "almonds_portion": "1 oz. (23 unidades)", "lentils_cooked": "Lentilhas, cozidas", "lentils_portion": "1/2 xícara", "grains_starches_label": "Grãos & Amidos", "quinoa_cooked": "Quinoa, cozida", "quinoa_portion": "1 xícara", "oatmeal": "Aveia", "oatmeal_portion": "1 xícara cozida", "whole_wheat_pasta": "<PERSON><PERSON><PERSON><PERSON> integral", "whole_wheat_pasta_portion": "1 xícara cozida", "brown_rice": "Arroz integral", "brown_rice_portion": "1 xícara cozida", "beverages_label": "Bebidas", "green_tea": "<PERSON><PERSON> verde", "green_tea_portion": "1 xícara", "almond_milk": "Le<PERSON> de <PERSON>", "almond_milk_portion": "1 xícara", "coconut_water": "Água de coco", "coconut_water_portion": "1 xícara", "red_wine": "<PERSON><PERSON> tinto", "red_wine_portion": "5 oz.", "calorie_table_note": "Nota: Os valores calóricos são aproximados e podem variar conforme métodos de preparo e marcas específicas.", "strategic_meal_planning": "Planejamento Estratégico de Refeições", "meal_period_label": "Período da Refeição", "cal_1300_plan": "Plano de 1.300 Calorias", "cal_1600_plan": "Plano de 1.600 Calorias", "cal_2100_plan": "Plano de 2.100 Calorias", "breakfast_label": "Café da manhã", "breakfast_1300": "1/2 xícara de iogurte grego com 1/2 xícara de mirtilos (130 cal)", "breakfast_1600": "1 xícara de aveia com 1/2 xícara de mirtilos (238 cal)", "breakfast_2100": "1 xícara de aveia com 1 kiwi, 1 oz. de amêndoas (458 cal)", "morning_snack_label": "<PERSON><PERSON><PERSON> da Man<PERSON>", "morning_snack_1300": "1 kiwi pequeno (56 cal)", "morning_snack_1600": "1 manga média (135 cal)", "morning_snack_2100": "1 manga média, 10 cerejas (232 cal)", "total_morning_label": "Total da Manhã", "total_morning_1300": "186 cal", "total_morning_1600": "373 cal", "total_morning_2100": "690 cal", "lunch_label": "Almoço", "lunch_1300": "2 xícaras de salada de espinafre com 3 oz. de salmão grelhado (189 cal)", "lunch_1600": "3 oz. de peito de peru, 1 xícara de abobrinha, 1/2 xícara de quinoa (264 cal)", "lunch_2100": "3 oz. de salmão grelhado, 1 xícara de arroz integral, 1 xícara de couve-flor (418 cal)", "afternoon_snack_label": "<PERSON><PERSON><PERSON>", "afternoon_snack_1300": "1 xícara de pimentão fatiado (28 cal)", "afternoon_snack_1600": "1/2 abacate (160 cal)", "afternoon_snack_2100": "1/2 abacate, 1 oz. de amêndoas (324 cal)", "total_midday_label": "Total do Meio-dia", "total_midday_1300": "217 cal", "total_midday_1600": "424 cal", "total_midday_2100": "742 cal", "dinner_label": "<PERSON><PERSON>", "dinner_1300": "3 oz. de peito de peru, 1 xícara de couve-flor (150 cal)", "dinner_1600": "3 oz. de salm<PERSON> g<PERSON>o, 1 xícara de batata-doce (287 cal)", "dinner_2100": "3 oz. de peito de peru, 1 xícara de macarrão integral, 1 xícara de espinafre (313 cal)", "evening_snack_label": "<PERSON><PERSON><PERSON> No<PERSON>", "evening_snack_1300": "1 xícara de chá verde (2 cal)", "evening_snack_1600": "1 xícara de água de coco (46 cal)", "evening_snack_2100": "1 xícara de iogurte grego (130 cal)", "total_evening_label": "Total da Noite", "total_evening_1300": "152 cal", "total_evening_1600": "333 cal", "total_evening_2100": "443 cal", "daily_total_label": "Total Diário", "daily_total_1300": "1.255 cal", "daily_total_1600": "1.630 cal", "daily_total_2100": "2.175 cal", "master_your_metabolism": "<PERSON><PERSON>abolis<PERSON>", "metabolism_description": "Entender seu metabolismo é fundamental para alcançar seus objetivos de saúde. Esta calculadora usa fórmulas cientificamente validadas para estimar sua Taxa Metabólica Basal (BMR) e o Gasto Energético Total Diário (TDEE).", "three_proven_formulas": "<PERSON><PERSON><PERSON><PERSON>s Comprovadas para BMR", "formulas_description": "Esta calculadora utiliza três equações bem pesquisadas para estimar seu BMR, cada uma com pontos fortes únicos dependendo do seu perfil:", "mifflin_st_jeor_title": "Equação de Mifflin-St Jeor", "male_formula_label": "<PERSON><PERSON><PERSON><PERSON>", "female_formula_label": "Feminino", "mifflin_st_jeor_note": "Considerada a mais precisa para a população geral, especialmente para não atletas.", "harris_benedict_title": "Equação Harris-<PERSON>", "male_calculation_label": "<PERSON><PERSON><PERSON><PERSON>", "female_calculation_label": "Feminino", "harris_benedict_note": "Uma fórmula confiável atualizada para precisão moderna, adequada para uma ampla gama de indivíduos.", "katch_mcardle_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "katch_mcardle_note": "Ideal para indivíduos com percentual de gordura corporal conhecido, pois considera a massa magra.", "tdee_transformation": "TDEE: Transformando BMR em Metas Atingíveis", "tdee_description": "Seu Gasto Energético Total Diário (TDEE) é seu BMR multiplicado por um fator de atividade que reflete seu estilo de vida. Isso fornece uma visão completa de suas necessidades calóricas diárias.", "activity_level_label": "Nível de Atividade", "description_label": "Descrição", "multiplier_label": "Multiplicador", "lightly_active_label": "Levemente Ativo", "moderately_active_label": "Moderadamente Ativo", "super_active_label": "Super Ativo", "super_active_description": "exercício muito intenso diariamente, ou trabalho físico", "tdee_result_description": "O resultado é uma estimativa precisa das calorias que você queima diariamente, que pode ser usada para adaptar sua dieta para perda de peso, manutenção ou ganho.", "strategic_weight_management": "Gestão Estratégica de Peso", "weight_management_description": "Para perder peso, é necessário um déficit calórico; para ganhar peso, um superávit. Esta calculadora fornece metas calóricas precisas para cada objetivo, garantindo progresso sustentável.", "critical_guidelines": "Diretrizes Críticas para Perda de Peso Segura:", "metabolic_suppression": "Evite déficits extremos para não desacelerar seu metabolismo.", "muscle_degradation": "Garanta ingestão adequada de proteína para preservar a massa muscular.", "nutrient_depletion": "Priorize alimentos ricos em nutrientes para evitar deficiências.", "rebound_weight_gain": "Evite dietas radicais para prevenir o reganho de peso.", "optimal_results_description": "Para resultados ideais, consulte um nutricionista ou dietista para personalizar ainda mais seu plano.", "nutrition_optimization_strategies": "Estratégias de Otimização Nutricional", "nutrient_density_priority": "Priorize alimentos ricos em nutrientes como vegetais, proteínas magras e grãos integrais.", "adequate_protein_intake": "Garanta ingestão adequada de proteína para apoiar a manutenção muscular e a saciedade.", "reject_restrictive_dieting": "Rejeite dietas excessivamente restritivas em favor de uma alimentação equilibrada e sustentável.", "consistent_daily_practices": "Acompanhe sua ingestão consistentemente para criar hábitos saudáveis a longo prazo.", "calculator_purpose": "Esta calculadora é um ponto de partida. Ajuste com base no seu progresso e consulte profissionais para orientação personalizada.", "precision_calorie_tracking": "Rastreamento Preciso de Calorias", "determine_metabolic_baseline": "Passo 1: Determine Sua Linha de Base Metabólica", "metabolic_baseline_description": "Use esta calculadora para encontrar seu BMR e TDEE com base em sua idade, gênero, peso, altura e nível de atividade.", "establish_weight_targets": "Passo 2: Estabeleça Suas Metas de Peso", "weight_targets_description": "Defina metas realistas para perda de peso, manutenção ou ganho, usando as recomendações calóricas fornecidas.", "implement_monitoring_systems": "Passo 3: Implemente Sistemas de Monitoramento", "monitoring_systems_description_1": "Acompanhe sua ingestão calórica usando aplicativos ou um diário alimentar para manter-se alinhado às suas metas.", "monitoring_systems_description_2": "Pese-se semanalmente e monitore tendências, não flutuações diárias.", "optimize_through_assessment": "Passo 4: Otimize Através da Avaliação", "assessment_description": "Reavalie suas necessidades calóricas a cada 4-6 semanas ou após mudanças significativas de peso para manter seu plano eficaz.", "caloric_balance_science": "A Ciência do Balanço Calórico", "energy_balance_fundamentals": "Fundamentos do Balanço Energético", "energy_balance_description": "O gerenciamento de peso é regido pelas calorias consumidas versus calorias gastas.", "thermic_effect_food": "Efeito Térmico dos Alimentos", "thermic_effect_description": "Alimentos diferentes exigem diferentes quantidades de energia para serem digeridos, impactando o total de calorias queimadas.", "satiety_food_quality": "Saciedade e Qualidade dos Alimentos", "satiety_description": "Alimentos ricos em fibras e proteínas promovem saciedade, ajudando você a seguir seu plano.", "twinkie_diet_case_study": "Exemplo: A 'Dieta Twinkie' mostrou que a perda de peso é possível com déficit calórico, mas dietas pobres em nutrientes prejudicam a saúde a longo prazo.", "bonus_benefits_tracking": "Benefícios Extras do Rastreamento de Calorias", "builds_nutritional_awareness": "Constrói consciência nutricional e hábitos alimentares conscientes.", "improves_portion_control": "Melhora o controle de porções por meio do rastreamento consistente.", "connects_food_exercise": "Conecta escolhas alimentares ao exercício para otimizar o balanço energético.", "zigzag_calorie_cycling": "Ciclagem de Calorias Zigzag", "zigzag_description": "A ciclagem de calorias zigzag envolve variar sua ingestão calórica diária enquanto mantém sua meta semanal para melhorar a flexibilidade metabólica e evitar platôs.", "example_label": "Exemplo", "weekly_target_label": "<PERSON><PERSON>", "weekly_target_value": "14.000 calorias (2.000 calorias/dia em média)", "option_a_label": "Opção A", "option_a_description": "7 dias a 2.000 calorias.", "option_b_label": "Opção B", "option_b_description": "5 dias a 1.800 calorias, 2 dias a 2.500 calorias.", "zigzag_adjustment_description": "<PERSON><PERSON> as op<PERSON><PERSON><PERSON> atingem a meta semanal, mas variam a ingestão diária para manter seu metabolismo dinâmico.", "zigzag_goal_label": "<PERSON><PERSON>", "better_metabolic_flexibility": "Melhor flexibilidade metabólica e adesão.", "diet_flexibility": "Mais flexibilidade no planejamento de refeições, especialmente para eventos sociais.", "prevents_metabolic_adaptation": "Previne adaptação metabólica de déficits prolongados.", "breaks_weight_loss_plateaus": "Quebra platôs de perda de peso variando a ingestão calórica.", "calorie_requirements_title": "Necessidades Calóricas por Estilo de Vida", "calorie_needs_vary_by": "As necessidades calóricas variam por fatores individuais, mas diretrizes gerais podem ajudar você a começar.", "factors_influence_calorie_needs": "Fatores que Influenciam as Necessidades Calóricas", "age_sex_weight_height": "Idade, sexo, peso e altura.", "activity_level": "Nível de atividade (sedentário a altamente ativo).", "health_status": "Estado de saúde, incluindo gravidez ou condições médicas.", "general_guidelines_label": "Diretrizes Gerais", "men_label": "Homens", "men_calorie_range": "2.000–3.000 calorias/dia", "women_label": "<PERSON><PERSON><PERSON><PERSON>", "women_calorie_range": "1.600–2.400 calorias/dia", "minimum_safe_intake": "Ingestão Mínima <PERSON>ra", "women_minimum_intake": "1.200 calorias/dia", "men_minimum_intake": "1.500 calorias/dia", "medically_supervised_warning": "Ingestões abaixo desses níveis devem ser supervisionadas por um médico.", "over_restricting_warning": "<PERSON><PERSON><PERSON> demais as calorias pode levar a deficiências nutricionais, perda muscular e desaceleração metabólica. Sempre priorize a saúde.", "not_all_calories_equal": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "calories_come_from": "As calorias vêm de diferentes macronutrientes, cada um com efeitos únicos no seu corpo:", "protein": "Proteína: 4 calorias/grama – apoia a reparação muscular e a saciedade.", "carbohydrates": "Carboidratos: 4 calorias/grama – principal fonte de energia.", "fat": "Gordura: 9 calorias/grama – essencial para hormônios e absorção de nutrientes.", "alcoholl": "Álcool: 7 calorias/grama – valor nutricional mínimo.", "nutrition_labels_accuracy": "Rótulos nutricionais fornecem contagens calóricas precisas, mas tamanhos de porção e métodos de preparo importam.", "high_calorie_foods": "Alimentos Ricos em Calorias", "high_calorie_description": "Densos em calorias, muitas vezes devido a gorduras ou açúcares. Use com moderação para controle de peso.", "avocados": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "nuts_and_seeds": "Nozes e sementes.", "fried_foods": "Alimentos fritos.", "sugary_foods": "Doces e lanches açucarados.", "low_calorie_foods": "Alimentos com Baixas Calorias", "many_vegetables": "<PERSON><PERSON><PERSON> vegetais (ex.: espinafre, abobrinha).", "some_fruits": "Algumas frutas (ex.: frutas vermelhas).", "lean_proteins": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> (ex.: peru, peixe).", "whole_grains": "Grãos integrais com moderação.", "leafy_greens": "Verduras de folhas para volume e nutrientes.", "empty_calories": "<PERSON><PERSON><PERSON>", "sugary_drinks": "Bebidas açucaradas (ex.: refrigerante).", "processed_snacks": "Lanches processados (ex.: batatas fritas, biscoitos).", "added_sugars": "Açúcares adicionados em alimentos embalados.", "solid_fats": "<PERSON><PERSON><PERSON><PERSON> (ex.: manteiga, margarina).", "alcohol": "Álcool com benefício nutricional mínimo.", "caloric_quality_matters": "Por Que a Qualidade Calórica Importa", "drinks_calorie_impact": "Bebidas como refrigerante ou álcool adicionam calorias sem saciedade, dificultando manter um déficit.", "build_balanced_plan": "Monte um Plano Balanceado", "focus_whole_foods": "Foque em alimentos integrais e não processados para densidade nutricional.", "limit_sugar_snacks": "Limite lanches e bebidas açucaradas.", "natural_portion_control": "Use alimentos ricos em fibras e proteínas para controle natural de porções.", "combine_calorie_counting_exercise": "Combine contagem de calorias com exercício para resultados sustentáveis.", "final_takeaway": "Conclusão Final", "no_one_size_fits_all": "Não existe uma abordagem única para nutrição. Use esta calculadora como ponto de partida, acompanhe seu progresso e ajuste conforme necessário para atender às suas necessidades únicas.", "track_wisely_eat_mindfully": "Acompanhe com sabedoria, coma com atenção e priorize a saúde a longo prazo.", "hero_title": "Bem-vindo aos Calculadores de Saúde", "hero_description": "Apoiamos a sua jornada de saúde com ferramentas baseadas em ciência.", "calorie_calculator_title": "Calculadora de Calorias", "calorie_calculator_description": "Descubra as suas necessidades calóricas diárias para perda de peso, manutenção ou ganho de massa muscular. Rápido, preciso e gratuito.", "calorie_calculator_link": "calorie-calculator/", "go_to_calculator": "Ir para a Calculadora de Calorias", "bmi_title": "Calculadora de BMI para adultos – Ferramenta precisa de BMI | MeetAugust", "bmi_meta_description": "Calcule seu BMI com nossa ferramenta avançada. Obtenha resultados imediatos e veja sua categoria de peso. Grátis, precisa e baseada em ciência.", "bmi_meta_keywords": "calculadora bmi, índice de massa corporal, categoria de peso, calculadora de saúde", "bmi_og_title": "Calculadora de BMI para adultos – Ferramenta precisa de BMI", "bmi_og_description": "Calcule seu BMI e veja sua categoria de peso instantaneamente.", "bmi_twitter_title": "Calculadora de BMI para adultos – Ferramenta precisa de BMI", "bmi_twitter_description": "Calcule seu BMI e veja sua categoria de peso instantaneamente.", "bmi_talk_to_august": "Fale com August", "bmi_page_title": "Calculadora de BMI para adultos", "bmi_for_everyone": "Para todos", "bmi_understanding_title": "Entendendo o BMI para adultos: O que você precisa saber", "bmi_understanding_text": "A calculadora de BMI para adultos é uma ferramenta confiável para avaliar se seu peso é adequado para sua altura. ...", "bmi_calculator_title": "Calculadora de IMC (BMI)", "bmi_calculator_description": "Verifique instantaneamente seu Índice de Massa Corporal (IMC) e veja em qual categoria de peso você se enquadra. Destinado a adultos com mais de 20 anos. Rápido, preciso e gratuito.", "bmi_calculator_link": "/calculators/bmi/", "go_to_bmi_calculator": "Ir para a Calculadora de IMC", "bmi_us_units": "Unidades dos EUA", "bmi_metric_units": "Unidades Métricas", "bmi_reset": "REDEFINIR", "bmi_height": "ALTURA", "bmi_feet": "pés (ft)", "bmi_inches": "polegadas (in)", "bmi_weight": "PESO", "bmi_pounds": "libras (lbs)", "bmi_calculate": "Calcular", "bmi_note": "Nota:", "bmi_note_text": "Esta calculadora de BMI requer JavaScript para funcionar corretamente. Se o seu navegador tiver o JavaScript desativado ou você encontrar problemas, pode calcular manualmente seu BMI com esta fórmula:", "bmi_formula": "BMI = (peso em quilogramas) / (altura em metros × altura em metros)", "bmi_formula_example": "<PERSON>r exemplo, se você pesa 70 kg e tem 1,75 m de altura, seu BMI é 22,9.", "bmi_free_calculator_title": "Calculadora gratuita de BMI para adultos (20+ anos)", "bmi_free_calculator_desc1": "Use esta calculadora de BMI para adultos para determinar instantaneamente seu BMI e ver em qual categoria de peso você se encontra — baixo peso, peso normal, sobrepeso ou obesidade. Esta ferramenta é projetada especificamente para adultos com mais de 20 anos.", "bmi_free_calculator_desc2": "O BMI é apenas uma das muitas ferramentas para avaliar a saúde geral e os riscos potenciais relacionados ao peso. Deve ser interpretado juntamente com outras avaliações clínicas, como histórico médico, h<PERSON><PERSON>os, exame físico e exames laboratoriais.", "bmi_learn_more_bmi": "Saiba mais sobre o Índice de Massa Corporal", "bmi_free_calculator_desc3": "e como ele se encaixa no seu perfil de saúde. Sempre consulte um profissional de saúde para aconselhamento personalizado — esta ferramenta é apenas para fins educacionais e não substitui o aconselhamento médico."}