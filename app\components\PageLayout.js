'use client';
import NavBar from './NavBar';
import HeroSection from './HeroSection';
import CategorySection from './CategorySection';
import Box from '@mui/material/Box';
import Footer from './Footer'; 

export default function PageLayout({ 
  heroProps,
  categoryData 
}) {
  return (
    <div>
      
      <NavBar />
      <HeroSection {...heroProps} />
      <Box sx={{ bgcolor: 'white' }}>
        <CategorySection {...categoryData} />
      </Box>
      <Footer />
    </div>
  );
}