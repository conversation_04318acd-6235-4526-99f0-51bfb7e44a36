import PageLayoutClient from '../../components/PageLayoutClient';
import { getConditionIndexMetaData } from '@/app/api/diseases-conditions/meta/getMetaDataIndex';
import translationStrings from './language/translations';
const logger = require('../../utils/logger');

export default async function DiseasesPage({ params }) {
  const language = params?.lang || 'en';
  const langStrings = translationStrings[language] || translationStrings.en;

  // Fetch metadata server-side
  let metaTitle = langStrings.title || '';
  let metaDescription = langStrings.title || '';

  try {
    const metadata = await getConditionIndexMetaData(language);
    if (metadata) {
      metaTitle = metadata.title || langStrings.title;
      metaDescription = metadata.description || langStrings.title;
    } else {
      logger.warn(`No home page SEO tags found in DB for language: ${language}`);
    }
  } catch (error) {
    logger.error("Error fetching home metadata:", error);
    // Default values already set above
  }

  const heroProps = {
    title: langStrings.title,
    description: langStrings.description,
    searchPlaceholder: langStrings.searchPlaceholder,
    browseByLetterText: langStrings.browseByLetter,
    baseUrl: `/${language}/diseases-conditions`,
    indices: {
      health_library: 'Health_Library',
    },
    tags: ['health_library', 'conditions']
  };

  const commonDiseasesData = {
    title: langStrings.commonDiseasesTitle,
    description: langStrings.commonDiseasesDescription,
    items: [
      {
        title: langStrings.type2Diabetes,
        description: langStrings.type2DiabetesDescription,
        href: `/${language}/diseases-conditions/view/type-2-diabetes`
      },
      {
        title: langStrings.hypertension,
        description: langStrings.hypertensionDescription,
        href: `/${language}/diseases-conditions/view/high-blood-pressure`
      },
      {
        title: langStrings.asthma,
        description: langStrings.asthmaDescription,
        href: `/${language}/diseases-conditions/view/asthma`
      },
      {
        title: langStrings.arthritis,
        description: langStrings.arthritisDescription,
        href: `/${language}/diseases-conditions/view/arthritis`
      },
      {
        title: langStrings.depression,
        description: langStrings.depressionDescription,
        href: `/${language}/diseases-conditions/view/depression`
      },
      {
        title: langStrings.heartDisease,
        description: langStrings.heartDiseaseDescription,
        href: `/${language}/diseases-conditions/view/heart-disease`
      },
      {
        title: langStrings.gerd,
        description: langStrings.gerdDescription,
        href: `/${language}/diseases-conditions/view/gerd`
      },
      {
        title: langStrings.migraine,
        description: langStrings.migraineDescription,
        href: `/${language}/diseases-conditions/view/migraine-headache`
      }
    ]
  };

  return (
    <PageLayoutClient
      heroProps={heroProps}
      categoryData={commonDiseasesData}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
    />
  );
}