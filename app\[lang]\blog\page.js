import PageLayoutClient from '../../components/PageLayoutClient';
import translationStrings from './language/translations';
import { getBlogIndexMetaData } from '@/app/api/blog/meta/getMetaDataIndex';
import { getFeaturedBlogs } from '@/app/api/blog/meta/getFeaturedBlogs';
const logger = require('../../utils/logger');

export default async function BlogPage(props) {
  const params = await props.params;
  const language = params.lang || 'en';
  const langStrings = translationStrings[language] || translationStrings.en;

  // Fetch metadata server-side
  let metaTitle = langStrings.blogTitle || '';
  let metaDescription = langStrings.blogTitle || '';

  try {
    const metadata = await getBlogIndexMetaData(language);
    if (metadata) {
      metaTitle = metadata.title || langStrings.blogTitle;
      metaDescription = metadata.description || langStrings.blogTitle;
    } else {
      logger.warn(`No home page SEO tags found in DB for language: ${language}`);
    }
  } catch (error) {
    logger.error("Error fetching home metadata:", error);
    // Default values already set above
  }

  const heroProps = {
    title: langStrings.blogTitle,
    description: langStrings.blogDescription,
    searchPlaceholder: langStrings.blogSearchPlaceholder,
    browseByLetterText: langStrings.browseByLetter,
    baseUrl: `/${language}/blog`,
    indices: {
      health_library: 'Health_Library',
    },
    tags: ['blogs']
  };

  // Fetch featured blogs from database
  let featuredBlogsData = {
    title: langStrings.featuredBlogsTitle,
    description: langStrings.featuredBlogsDescription,
    items: []
  };

  try {
    const featuredBlogs = await getFeaturedBlogs(language);
    featuredBlogsData.items = featuredBlogs;
  } catch (error) {
    logger.error('Error fetching featured blogs:', error);
  }

  return (
    <PageLayoutClient
      heroProps={heroProps}
      categoryData={featuredBlogsData}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
    />
  );
}
