'use client';
const logger = require('../../../../utils/logger');
import { useState } from 'react';
import NavBar from '@/app/components/NavBar';
import AlphabeticalList from '@/app/components/AlphabeticalList';
import AlphabetGrid from '@/app/components/AlphabetGrid';
import { Box, Container, Typography, Grid } from '@mui/material';
import SearchBar from '@/app/components/SearchBar';
import Footer from '@/app/components/Footer';
import { Helmet } from 'react-helmet-async';
import { fetchByLetter } from '@/app/lib/api';

function decodeLetter(letter) {
  try {
    return decodeURIComponent(letter);
  } catch (e) {
    logger.error("Failed to decode URI Component", letter, e);
    return letter;
  }
}

export default function DiseasesByLetterClient({ 
  initialData, 
  letter, 
  language, 
  metaIndexTitle, 
  metaDescription, 
  langStrings, 
  error
}) {
  const [conditions, setConditions] = useState(initialData.items);
  const [pagination, setPagination] = useState(initialData.pagination);
  const decodedLetter = decodeLetter(letter);
  logger.info('Client Component Conditions:', conditions);

  const handlePageChange = async (page) => {
    try {
      const data = await fetchByLetter('diseases-conditions', letter, language, page);
      setConditions(data.items);
      setPagination(data.pagination);
    } catch (error) {
      logger.error('Error fetching page:', error);
    }
  };

  return (
    <div>
      <Helmet>
        <title>{metaIndexTitle} {decodedLetter}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={`${metaIndexTitle} ${decodedLetter}`} />
        <meta property="og:description" content={metaDescription} />
        <link rel="canonical" href={`https://www.meetaugust.ai/${language}/library/diseases-conditions`} />
        <meta name="robots" content="noindex, follow" />
      </Helmet>
      <NavBar />
      
      {/* Hero Section */}
      <Box 
        sx={{ 
          backgroundColor: '#F7F6F4',
          py: 6,
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography 
                variant="h3" 
                component="h1" 
                gutterBottom
                sx={{ 
                  fontWeight: 'bold',
                  mb: 3
                }}
              >
                {langStrings.title}
              </Typography>
              <Typography 
                variant="h6" 
                sx={{ 
                  color: 'text.secondary',
                  mb: 4
                }}
              >
                {langStrings.description}
              </Typography>
              <SearchBar 
                placeholder={langStrings.searchPlaceholder} 
                indices={{
                  health_library: 'Health_Library',
                }} 
                tags={['health_library', 'conditions']} 
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ 
                backgroundColor: 'white',
                p: 3,
                borderRadius: 2,
                boxShadow: 1
              }}>
                <Typography 
                  variant="h6" 
                  sx={{ mb: 2 }}
                >
                  {langStrings.browseByLetter}
                </Typography>
                <AlphabetGrid baseUrl={`/${language}/diseases-conditions`} />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Alphabetical List Section */}
      <Box 
        sx={{ 
          backgroundColor: 'white',
          py: 4
        }}
      >
        <Container maxWidth="lg">
        {conditions.length > 0 ? (
      <AlphabeticalList 
        items={conditions} 
        letter={letter}
        section="diseases-conditions"
        pagination={pagination}
        onPageChange={handlePageChange}
      />
    ) : (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          {langStrings.noConditionsFoundMessage} {/* Use the pre-computed message */}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {langStrings.tryAnother}
        </Typography>
      </Box>
    )}
        </Container>
      </Box>
      <Footer />
    </div>
  );
}
