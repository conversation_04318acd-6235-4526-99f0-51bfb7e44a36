var settings = {
  resultsUnit: "calories",
  bmrFormula: "mifflin", // Default to Mifflin St Jeor
  unitSystem: "metric", // "metric" or "us"
};

// Unit conversion functions
function convertHeight(value, fromUnit, toUnit) {
  if (fromUnit === toUnit) return value;

  if (fromUnit === "cm" && toUnit === "feet-inches") {
    var totalInches = value / 2.54;
    var feet = Math.floor(totalInches / 12);
    var inches = Math.round(totalInches % 12);
    return { feet: feet, inches: inches };
  } else if (fromUnit === "feet-inches" && toUnit === "cm") {
    return Math.round(value * 2.54);
  }
  return value;
}

function convertWeight(value, fromUnit, toUnit) {
  if (fromUnit === toUnit) return value;

  if (fromUnit === "kg" && toUnit === "lbs") {
    return Math.round(value * 2.20462);
  } else if (fromUnit === "lbs" && toUnit === "kg") {
    return Math.round(value / 2.20462);
  }
  return value;
}

// Tab switching with unit conversion
var tabs = document.querySelectorAll(".tab-button");
tabs.forEach(function (tab) {
  tab.addEventListener("click", function () {
    var newUnit = this.getAttribute("data-unit");

    // Don't do anything if clicking the same unit
    if (settings.unitSystem === newUnit) return;

    // Update active tab
    tabs.forEach(function (t) {
      t.classList.remove("active");
    });
    this.classList.add("active");

    // Convert and switch units
    switchUnits(newUnit);
    settings.unitSystem = newUnit;
  });
});

function switchUnits(unitSystem) {
  if (unitSystem === "us") {
    // Convert from metric to US
    var currentHeightCm =
      parseFloat(document.getElementById("height-cm").value) || 180;
    var currentWeightKg =
      parseFloat(document.getElementById("weight-kg").value) || 65;

    // Convert height
    var heightFeetInches = convertHeight(currentHeightCm, "cm", "feet-inches");
    document.getElementById("height-feet").value = heightFeetInches.feet;
    document.getElementById("height-inches").value = heightFeetInches.inches;

    // Convert weight
    var weightLbs = convertWeight(currentWeightKg, "kg", "lbs");
    document.getElementById("weight-lbs").value = weightLbs;

    // Show US inputs, hide metric
    document.getElementById("height-us").style.display = "block";
    document.getElementById("height-metric").style.display = "none";
    document.getElementById("weight-us").style.display = "block";
    document.getElementById("weight-metric").style.display = "none";
  } else {
    // Convert from US to metric
    var currentFeet =
      parseFloat(document.getElementById("height-feet").value) || 5;
    var currentInches =
      parseFloat(document.getElementById("height-inches").value) || 10;
    var currentWeightLbs =
      parseFloat(document.getElementById("weight-lbs").value) || 165;

    // Convert height
    var totalInches = currentFeet * 12 + currentInches;
    var heightCm = convertHeight(totalInches, "feet-inches", "cm");
    document.getElementById("height-cm").value = heightCm;

    // Convert weight
    var weightKg = convertWeight(currentWeightLbs, "lbs", "kg");
    document.getElementById("weight-kg").value = weightKg;

    // Show metric inputs, hide US
    document.getElementById("height-us").style.display = "none";
    document.getElementById("height-metric").style.display = "block";
    document.getElementById("weight-us").style.display = "none";
    document.getElementById("weight-metric").style.display = "block";
  }
}

// Ensure correct initial state on page load
document.addEventListener("DOMContentLoaded", function () {
  switchUnits(settings.unitSystem);
});

// Enhanced calculation function matching calculator.net exactly
function calculateBMR(gender, weight, height, age, equation) {
  let BMR;
  switch (equation) {
    case "mifflin":
      if (gender === "male") {
        BMR = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        BMR = 10 * weight + 6.25 * height - 5 * age - 161;
      }
      break;
    case "harris":
      if (gender === "male") {
        BMR = 13.397 * weight + 4.799 * height - 5.677 * age + 88.362;
      } else {
        BMR = 9.247 * weight + 3.098 * height - 4.33 * age + 447.593;
      }
      break;
    case "katch":
      // Katch-McArdle requires body fat, but since it's removed, fallback to Mifflin
      if (gender === "male") {
        BMR = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        BMR = 10 * weight + 6.25 * height - 5 * age - 161;
      }
      break;
    default:
      throw new Error('Equation must be "mifflin", "harris", or "katch"');
  }
  return BMR;
}

function calculateTDEE(bmr, activityLevel) {
  // Activity factors matching calculator.net exactly based on the reference URL
  const activityFactors = {
    sedentary: 1.2,
    light: 1.375,
    moderate: 1.465, // This matches the reference URL cactivity=1.465
    very: 1.725,
    super: 1.9,
  };

  const activityFactor = activityFactors[activityLevel];
  if (!activityFactor) {
    throw new Error("Invalid activity level");
  }

  return bmr * activityFactor;
}

function calculateCalorieTargets(tdee) {
  // Updated targets for weight gain:
  // Mild gain: 112% (0.25 kg/week)
  // Weight gain: 124% (0.5 kg/week)
  // Fast gain: 148% (1 kg/week)
  return {
    maintain: Math.round(tdee),
    mildLoss: Math.round(tdee * 0.86), // 86% for mild weight loss (0.25 kg/week)
    weightLoss: Math.round(tdee * 0.72), // 72% for weight loss (0.5 kg/week)
    extremeLoss: Math.round(tdee * 0.44), // 44% for extreme weight loss (1 kg/week)
    mildGain: Math.round(tdee * 1.12), // 112% for mild weight gain (0.25 kg/week)
    weightGain: Math.round(tdee * 1.24), // 124% for weight gain (0.5 kg/week)
    fastGain: Math.round(tdee * 1.48), // 148% for fast weight gain (1 kg/week)
  };
}

// Convert calories to kilojoules
function convertToKilojoules(calories) {
  return Math.round(calories * 4.1868);
}

// Format number with commas
function formatNumber(num) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Get activity multiplier
function getActivityMultiplier(activity) {
  switch (activity) {
    case "sedentary":
      return 1.2;
    case "light":
      return 1.375;
    case "moderate":
      return 1.465; // Updated to match calculator.net
    case "very":
      return 1.725;
    case "super":
      return 1.9;
    default:
      return 1.465;
  }
}

// Update activity level highlighting
function updateActivityHighlight(selectedActivity) {
  // Reset all activity cards to default style
  var activityCards = document.querySelectorAll(
    '.bmr-info-section [style*="padding: 8px"]'
  );
  activityCards.forEach(function (card) {
    card.style.backgroundColor = "white";
    card.style.border = "1px solid #e5e7eb";
  });

  // Highlight the selected activity
  var activityMap = {
    sedentary: 0,
    light: 1,
    moderate: 2,
    very: 4,
    super: 5,
  };

  var selectedIndex = activityMap[selectedActivity];
  if (selectedIndex !== undefined && activityCards[selectedIndex]) {
    activityCards[selectedIndex].style.backgroundColor = "#e0f2e7";
    activityCards[selectedIndex].style.border = "2px solid #206e55";
  }
}

// Update results display
function updateResults(tdee, userWeight, userActivity) {
  var unit =
    settings.resultsUnit === "calories" ? "Calories/day" : "Kilojoules/day";
  var conversionFactor = settings.resultsUnit === "calories" ? 1 : 4.1868;

  // Calculate all the different calorie targets using the correct percentages
  var calorieTargets = calculateCalorieTargets(tdee);
  var targets = {
    maintain: Math.round(calorieTargets.maintain * conversionFactor),
    mildLoss: Math.round(calorieTargets.mildLoss * conversionFactor),
    weightLoss: Math.round(calorieTargets.weightLoss * conversionFactor),
    extremeLoss: Math.round(calorieTargets.extremeLoss * conversionFactor),
    mildGain: Math.round(calorieTargets.mildGain * conversionFactor),
    weightGain: Math.round(calorieTargets.weightGain * conversionFactor),
    fastGain: Math.round(calorieTargets.fastGain * conversionFactor),
  };

  // Update calorie displays
  document.getElementById("maintain-calories").textContent = formatNumber(
    targets.maintain
  );
  document.getElementById("mild-loss-calories").textContent = formatNumber(
    targets.mildLoss
  );
  document.getElementById("weight-loss-calories").textContent = formatNumber(
    targets.weightLoss
  );
  document.getElementById("extreme-loss-calories").textContent = formatNumber(
    targets.extremeLoss
  );
  document.getElementById("mild-gain-calories").textContent = formatNumber(
    targets.mildGain
  );
  document.getElementById("weight-gain-calories").textContent = formatNumber(
    targets.weightGain
  );
  document.getElementById("fast-gain-calories").textContent = formatNumber(
    targets.fastGain
  );

  // Update BMR display
  var bmrValue = Math.round(tdee / getActivityMultiplier(userActivity));
  document.getElementById("bmr-value").textContent = formatNumber(bmrValue);

  // Update activity level highlighting
  updateActivityHighlight(userActivity);

  // Update unit labels for calories
  var calorieUnitLabels = document.querySelectorAll(".unit-label");
  calorieUnitLabels.forEach(function (label) {
    if (
      label.textContent.includes("Calories") ||
      label.textContent.includes("Kilojoules")
    ) {
      label.textContent = unit;
    }
  });

  // Calculate and update calorie percentages
  document
    .querySelector("#mild-loss-calories")
    .parentNode.querySelector(".percentage").textContent =
    Math.round((targets.mildLoss / targets.maintain) * 100) + "%";
  document
    .querySelector("#weight-loss-calories")
    .parentNode.querySelector(".percentage").textContent =
    Math.round((targets.weightLoss / targets.maintain) * 100) + "%";
  document
    .querySelector("#extreme-loss-calories")
    .parentNode.querySelector(".percentage").textContent =
    Math.round((targets.extremeLoss / targets.maintain) * 100) + "%";
  document
    .querySelector("#mild-gain-calories")
    .parentNode.querySelector(".percentage").textContent =
    Math.round((targets.mildGain / targets.maintain) * 100) + "%";
  document
    .querySelector("#weight-gain-calories")
    .parentNode.querySelector(".percentage").textContent =
    Math.round((targets.weightGain / targets.maintain) * 100) + "%";
  document
    .querySelector("#fast-gain-calories")
    .parentNode.querySelector(".percentage").textContent =
    Math.round((targets.fastGain / targets.maintain) * 100) + "%";

  // Show results container and hide old result
  document.getElementById("results-container").style.display = "block";
  document.getElementById("result").style.display = "none";
}

// Helper function to get current height and weight in metric units
function getCurrentHeightWeight() {
  var height, weight;

  if (settings.unitSystem === "us") {
    // Get US values and convert to metric
    var feet = parseFloat(document.getElementById("height-feet").value) || 5;
    var inches =
      parseFloat(document.getElementById("height-inches").value) || 10;
    var totalInches = feet * 12 + inches;
    height = totalInches * 2.54; // Convert to cm

    var weightLbs =
      parseFloat(document.getElementById("weight-lbs").value) || 165;
    weight = weightLbs / 2.20462; // Convert to kg
  } else {
    // Get metric values directly
    height = parseFloat(document.getElementById("height-cm").value) || 180;
    weight = parseFloat(document.getElementById("weight-kg").value) || 65;
  }

  return { height: height, weight: weight };
}

// Calculate button
document
  .querySelector(".calculate-button")
  .addEventListener("click", function () {
    try {
      var age = parseInt(document.getElementById("age").value);
      var gender = document.querySelector('input[name="gender"]:checked').value;
      var heightWeight = getCurrentHeightWeight();
      var height = heightWeight.height;
      var weight = heightWeight.weight;
      var activity = document.getElementById("activity").value;

      // Calculate BMR using the new function
      var bmr = calculateBMR(
        gender,
        weight,
        height,
        age,
        settings.bmrFormula
      );

      // Calculate TDEE
      var tdee = calculateTDEE(bmr, activity);

      // Update the enhanced results display
      updateResults(tdee, weight, activity);

      // Smooth scroll to results
      setTimeout(function () {
        var resultsContainer = document.getElementById("results-container");
        if (resultsContainer && resultsContainer.style.display === "block") {
          resultsContainer.scrollIntoView({ behavior: "smooth" });
        }
      }, 100);
    } catch (error) {
      alert("Error: " + error.message);
    }
  });

// Clear button
document.querySelector(".clear-button").addEventListener("click", function () {
  // Reset form values for both unit systems
  document.getElementById("age").value = "25";
  document.querySelector('input[name="gender"][value="male"]').checked = true;

  // Reset height values
  document.getElementById("height-feet").value = "5";
  document.getElementById("height-inches").value = "10";
  document.getElementById("height-cm").value = "180";

  // Reset weight values
  document.getElementById("weight-lbs").value = "165";
  document.getElementById("weight-kg").value = "65";

  // Reset activity
  document.getElementById("activity").selectedIndex = 2; // Moderate activity

  // Clear results
  document.getElementById("result").innerHTML = "";
  document.getElementById("results-container").style.display = "none";
  document.getElementById("result").style.display = "block";
});

// Settings inline functionality
var settingsContainer = document.getElementById("settings-container");
var settingsLink = document.getElementById("settings-link");
var collapseButton = document.getElementById("collapse-settings");
var isSettingsVisible = false;

settingsLink.addEventListener("click", function (e) {
  e.preventDefault();
  isSettingsVisible = !isSettingsVisible;

  if (isSettingsVisible) {
    settingsContainer.style.display = "block";
    this.textContent = "− Settings";
  } else {
    settingsContainer.style.display = "none";
    this.textContent = "+ Settings";
  }
});

// Collapse button functionality
collapseButton.addEventListener("click", function (e) {
  e.preventDefault();
  settingsContainer.style.display = "none";
  settingsLink.textContent = "+ Settings";
  isSettingsVisible = false;
});

// Function to recalculate results if they are currently displayed
function recalculateIfNeeded() {
  if (document.getElementById("results-container").style.display === "block") {
    // Trigger recalculation
    document.querySelector(".calculate-button").click();
  }
}

// Settings form handlers
document
  .querySelectorAll('input[name="results-unit"]')
  .forEach(function (radio) {
    radio.addEventListener("change", function () {
      settings.resultsUnit = this.value;
      recalculateIfNeeded();
    });
  });

document
  .querySelectorAll('input[name="bmr-formula"]')
  .forEach(function (radio) {
    radio.addEventListener("change", function () {
      settings.bmrFormula = this.value;
      recalculateIfNeeded();
    });
  });

// Weight gain toggle functionality
var weightGainToggle = document.getElementById("weight-gain-toggle");
var weightGainSection = document.getElementById("weight-gain-section");
var isWeightGainVisible = false;

if (weightGainToggle && weightGainSection) {
  weightGainToggle.addEventListener("click", function (e) {
    e.preventDefault();
    isWeightGainVisible = !isWeightGainVisible;

    if (isWeightGainVisible) {
      weightGainSection.style.display = "block";
      this.textContent = "Hide info for weight gain";
      // Add visual confirmation
      // weightGainSection.style.border = "2px solid #416955";
    } else {
      weightGainSection.style.display = "none";
      this.textContent = "Show info for weight gain";
      weightGainSection.style.border = "none";
    }
  });

  // Initialize weight gain as hidden
  weightGainSection.style.display = "none";
  weightGainToggle.textContent = "Show info for weight gain";
}

// Food Energy Converter
function convertEnergy() {
  var value = parseFloat(document.getElementById("converter-value").value) || 0;
  var fromUnit = document.getElementById("from-unit").value;
  var toUnit = document.getElementById("to-unit").value;

  // Conversion factors to kilojoules
  var toKJ = {
    kcal: 4.1868,
    cal: 0.0041868,
    kj: 1,
    j: 0.001,
  };

  // Convert to kilojoules first, then to target unit
  var kj = value * toKJ[fromUnit];
  var result = kj / toKJ[toUnit];

  document.getElementById("converted-value").textContent = result.toFixed(4);
}

var converterValue = document.getElementById("converter-value");
var fromUnit = document.getElementById("from-unit");
var toUnit = document.getElementById("to-unit");

if (converterValue && fromUnit && toUnit) {
  converterValue.addEventListener("input", convertEnergy);
  fromUnit.addEventListener("change", convertEnergy);
  toUnit.addEventListener("change", convertEnergy);
  convertEnergy();
}

// Header scroll effect
function handleScroll() {
  const header = document.querySelector("header");
  if (header) {
    if (window.scrollY === 0) {
      header.classList.remove("scrolled");
    } else {
      header.classList.add("scrolled");
    }
  }
}

// Add scroll event listener
window.addEventListener("scroll", handleScroll);

// Initialize header state
handleScroll();

// PDF Download Function
function downloadResultsPDF() {
  try {
    // Check if results are available
    const resultsContainer = document.getElementById("results-container");
    if (!resultsContainer || resultsContainer.style.display === "none") {
      alert("Please calculate your results first before downloading.");
      return;
    }

    // Initialize jsPDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Set up PDF styling - keeping original colors
    const primaryColor = [32, 110, 85]; // #206e55
    const textColor = [17, 24, 39]; // #111827
    const lightGray = [107, 114, 128]; // #6b7280
    const backgroundColor = [248, 250, 252]; // Light background
    const borderColor = [229, 231, 235]; // Light border

    // Page layout constants
    const pageWidth = 210;
    const leftMargin = 20;
    const rightMargin = 20;
    const contentWidth = pageWidth - leftMargin - rightMargin;

    // Add header with better spacing
    let yPosition = 25;
    doc.setFontSize(20);
    doc.setTextColor(...primaryColor);
    doc.setFont(undefined, "bold");
    doc.text("Daily Calorie Requirements", leftMargin, yPosition);

    yPosition += 12;
    doc.setFontSize(12);
    doc.setTextColor(...lightGray);
    doc.setFont(undefined, "normal");
    doc.text("Generated by MeetAugust Calculator", leftMargin, yPosition);

    // Add line separator with proper spacing
    yPosition += 8;
    doc.setDrawColor(...primaryColor);
    doc.setLineWidth(0.5);
    doc.line(leftMargin, yPosition, leftMargin + contentWidth, yPosition);

    yPosition += 20;

    // Get user input data
    const age = document.getElementById("age").value;
    const gender = document.querySelector(
      'input[name="gender"]:checked'
    )?.value;
    const activity = document.getElementById("activity").value;

    // Get height and weight based on current unit system
    const heightWeight = getCurrentHeightWeight();
    const unitSystem = settings.unitSystem;

    // Add user input section with better formatting
    doc.setFontSize(14);
    doc.setTextColor(...textColor);
    doc.setFont(undefined, "bold");
    doc.text("Your Information:", leftMargin, yPosition);
    yPosition += 15;

    // Format height and weight display
    let heightDisplay, weightDisplay;
    if (unitSystem === "us") {
      // Convert height from cm to feet and inches
      const totalInches = heightWeight.height / 2.54;
      const feet = Math.floor(totalInches / 12);
      const inches = Math.round(totalInches % 12);
      heightDisplay = `${feet}' ${inches}"`;
      // Convert weight from kg to lbs
      weightDisplay = `${Math.round(heightWeight.weight * 2.20462)} lbs`;
    } else {
      heightDisplay = `${Math.round(heightWeight.height)} cm`;
      weightDisplay = `${Math.round(heightWeight.weight)} kg`;
    }

    const userInfo = [
      `Age: ${age} years`,
      `Gender: ${
        gender
          ? gender.charAt(0).toUpperCase() + gender.slice(1)
          : "Not specified"
      }`,
      `Height: ${heightDisplay}`,
      `Weight: ${weightDisplay}`,
      `Activity Level: ${getActivityLevelText(activity)}`,
    ].filter(Boolean);

    // Create background box for user info
    const userInfoHeight = userInfo.length * 8 + 15;
    doc.setFillColor(...backgroundColor);
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.3);
    doc.roundedRect(
      leftMargin,
      yPosition - 5,
      contentWidth,
      userInfoHeight,
      2,
      2,
      "FD"
    );

    // Add user info with consistent spacing
    yPosition += 5;
    doc.setFontSize(11);
    doc.setTextColor(...textColor);
    doc.setFont(undefined, "normal");

    userInfo.forEach((info) => {
      doc.text(info, leftMargin + 10, yPosition);
      yPosition += 8;
    });

    yPosition += 20;

    // Add results section with better formatting
    doc.setFontSize(14);
    doc.setTextColor(...textColor);
    doc.setFont(undefined, "bold");
    doc.text("Your Daily Calorie Targets:", leftMargin, yPosition);
    yPosition += 20;

    // Get results table data
    const resultsTable = document.querySelector(".results-table tbody");
    if (resultsTable) {
      const rows = resultsTable.querySelectorAll("tr");

      // Calculate table dimensions with proper spacing
      const tableHeight = rows.length * 22 + 25;

      // Create table background with rounded corners
      doc.setFillColor(255, 255, 255);
      doc.setDrawColor(...borderColor);
      doc.setLineWidth(0.5);
      doc.roundedRect(
        leftMargin,
        yPosition - 5,
        contentWidth,
        tableHeight,
        8,
        8,
        "FD"
      );

      yPosition += 15;

      rows.forEach((row, index) => {
        const goalCell = row.querySelector("td:first-child");
        const caloriesCell = row.querySelector("td:nth-child(2)");

        if (goalCell && caloriesCell) {
          const goal = goalCell.textContent.trim();
          const calories = caloriesCell.textContent.trim();

          // Extract calories number
          const caloriesNumber = calories.match(/[\d,]+/)?.[0] || calories;

          // Parse multi-line goals
          const goalLines = goal
            .split("\n")
            .map((line) => line.trim())
            .filter((line) => line);
          const mainGoal = goalLines[0];
          const subGoal = goalLines.length > 1 ? goalLines[1] : "";

          // Main goal name - left aligned
          doc.setFontSize(12);
          doc.setTextColor(...primaryColor);
          doc.setFont(undefined, "bold");
          doc.text(mainGoal, leftMargin + 20, yPosition);

          // Sub goal - smaller text, indented
          if (subGoal) {
            doc.setFontSize(10);
            doc.setTextColor(...primaryColor);
            doc.setFont(undefined, "normal");
            doc.text(subGoal, leftMargin + 35, yPosition + 8);
          }

          // Calories value - right aligned
          doc.setFontSize(14);
          doc.setTextColor(...textColor);
          doc.setFont(undefined, "bold");

          const caloriesText = caloriesNumber;
          const unitsText = " calories/day";

          // Calculate text widths for proper alignment
          const caloriesTextWidth = doc.getTextWidth(caloriesText);
          doc.setFontSize(10);
          const unitsTextWidth = doc.getTextWidth(unitsText);

          // Position from right edge with proper spacing
          const totalTextWidth = caloriesTextWidth + unitsTextWidth;
          const caloriesX = leftMargin + contentWidth - 20 - totalTextWidth;

          // Draw calories number
          doc.setFontSize(14);
          doc.setFont(undefined, "bold");
          doc.text(caloriesText, caloriesX, yPosition + (subGoal ? 4 : 0));

          // Draw units in smaller, lighter text
          doc.setFontSize(10);
          doc.setTextColor(...lightGray);
          doc.setFont(undefined, "normal");
          doc.text(
            unitsText,
            caloriesX + caloriesTextWidth,
            yPosition + (subGoal ? 4 : 0)
          );

          yPosition += 22;
        }
      });
    }

    // Add footer with better spacing
    yPosition += 30;

    // Add a subtle separator line
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.3);
    doc.line(
      leftMargin,
      yPosition - 15,
      leftMargin + contentWidth,
      yPosition - 15
    );

    // Footer information with proper alignment
    doc.setFontSize(9);
    doc.setTextColor(...lightGray);
    doc.setFont(undefined, "normal");

    const generatedText = "Generated on: " + new Date().toLocaleDateString();
    const websiteText = "Visit: www.meetaugust.ai";

    doc.text(generatedText, leftMargin, yPosition);

    // Right align website text
    const websiteTextWidth = doc.getTextWidth(websiteText);
    doc.text(
      websiteText,
      leftMargin + contentWidth - websiteTextWidth,
      yPosition
    );

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `calorie-requirements-${timestamp}.pdf`;

    // Save the PDF
    doc.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
    alert("Sorry, there was an error generating the PDF. Please try again.");
  }
}

// Helper function to get activity level text
function getActivityLevelText(activity) {
  const activityTexts = {
    sedentary: "Sedentary (little/no exercise)",
    light: "Light Exercise (1-3 days/week)",
    moderate: "Moderate Exercise (3-5 days/week)",
    very: "Heavy Exercise (6-7 days/week)",
    super: "Very Heavy Exercise (2x/day, intense)",
  };
  return activityTexts[activity] || activity;
}
