<!DOCTYPE html>
<html lang="{{bra_size_cal_lang}}">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>{{bra_size_cal_title}}</title>
    <meta name="description" content="{{bra_size_cal_meta_description}}" />
    <meta name="keywords" content="{{bra_size_cal_meta_keywords}}" />
    <meta name="author" content="{{bra_size_cal_meta_author}}" />
    <meta property="og:title" content="{{bra_size_cal_og_title}}" />
    <meta property="og:description" content="{{bra_size_cal_og_description}}" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{bra_size_cal_twitter_title}}" />
    <meta name="twitter:description" content="{{bra_size_cal_twitter_description}}" />
    <link rel="canonical" href="{{bra_size_cal_canonical_url}}" />
    <link rel="stylesheet" href="style.css">
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img width="200" src="{{bra_size_cal_logo_url}}" alt="{{bra_size_cal_logo_alt}}" />
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select" style="border:1px solid #e5e7eb;border-radius:6px;padding:6px 8px;font-size:14px;color:#374151;background:#fff;outline:none;"></select>
          </div>
          <a href="{{bra_size_cal_talk_to_august_url}}" class="talk-to-august">{{bra_size_cal_talk_to_august}}</a>
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">{{bra_size_cal_page_title}}</h1>
        </div>
        <div class="content-grid">
          <div class="info-section">
            <h2>{{bra_size_cal_how_to_measure}}</h2>
            <div class="measurement-steps">
              <div class="step-card">
                <div class="step-icon">1</div>
                <div class="step-content">
                  <strong>{{bra_size_cal_band_size}}</strong>
                  <p style="padding: 0;margin: 0;">
                    {{bra_size_cal_measure_underbust}} <br>
                    {{bra_size_cal_round_nearest}} <br>
                    <span class="badge">{{bra_size_cal_if_even}}</span>
                    <span class="badge">{{bra_size_cal_if_odd}}</span><br>
                    <span style="color:var(--primary-color);font-weight:500;">{{bra_size_cal_band_size_result}}</span>
                  </p>
                </div>
              </div>
              <div class="step-card">
                <div class="step-icon">2</div>
                <div class="step-content">
                  <strong>{{bra_size_cal_cup_size}}</strong>
                  <p style="padding: 0;margin: 0;">
                    {{bra_size_cal_measure_overbust}} <br>
                    {{bra_size_cal_subtract_band}} <br>
                    <span style="color:var(--primary-color);font-weight:500;">{{bra_size_cal_cup_size_result}}</span>
                  </p>
                </div>
              </div>
              <div class="step-card tip-card">
                <div class="step-icon">💡</div>
                <div class="step-content">
                  <strong>{{bra_size_cal_tip}}</strong>
                  <p style="padding: 0;margin: 0;">
                    {{bra_size_cal_non_padded_bra}} <br>
                    {{bra_size_cal_round_up}}
                  </p>
                </div>
              </div>
            </div>
            <h3 style="margin-top:2em;">{{bra_size_cal_chart_inches}}</h3>
            <table class="bmi-ranges-table" aria-label="{{bra_size_cal_chart_inches_aria}}">
              <thead>
                <tr>
                  <th>{{bra_size_cal_band_size}}</th>
                  <th>{{bra_size_cal_under_bust_in}}</th>
                  <th>{{bra_size_cal_over_bust_a}}</th>
                  <th>{{bra_size_cal_over_bust_b}}</th>
                  <th>{{bra_size_cal_over_bust_c}}</th>
                  <th>{{bra_size_cal_over_bust_d}}</th>
                  <th>{{bra_size_cal_over_bust_dd}}</th>
                  <th>{{bra_size_cal_over_bust_f}}</th>
                  <th>{{bra_size_cal_over_bust_g}}</th>
                </tr>
              </thead>
              <tbody>
                <tr><td>28</td><td>23–24</td><td>28–29</td><td>29–30</td><td>30–31</td><td>31–32</td><td>32–33</td><td>33–34</td><td>34–35</td></tr>
                <tr><td>30</td><td>25–26</td><td>30–31</td><td>31–32</td><td>32–33</td><td>33–34</td><td>34–35</td><td>35–36</td><td>36–37</td></tr>
                <tr><td>32</td><td>27–28</td><td>32–33</td><td>33–34</td><td>34–35</td><td>35–36</td><td>36–37</td><td>37–38</td><td>38–39</td></tr>
                <tr><td>34</td><td>29–30</td><td>34–35</td><td>35–36</td><td>36–37</td><td>37–38</td><td>38–39</td><td>39–40</td><td>40–41</td></tr>
                <tr><td>36</td><td>31–32</td><td>36–37</td><td>37–38</td><td>38–39</td><td>39–40</td><td>40–41</td><td>41–42</td><td>42–43</td></tr>
                <tr><td>38</td><td>33–34</td><td>38–39</td><td>39–40</td><td>40–41</td><td>41–42</td><td>42–43</td><td>43–44</td><td>44–45</td></tr>
                <tr><td>40</td><td>35–36</td><td>40–41</td><td>41–42</td><td>42–43</td><td>43–44</td><td>44–45</td><td>45–46</td><td>46–47</td></tr>
                <tr><td>42</td><td>37–38</td><td>42–43</td><td>43–44</td><td>44–45</td><td>45–46</td><td>46–47</td><td>47–48</td><td>48–49</td></tr>
                <tr><td>44</td><td>39–40</td><td>44–45</td><td>45–46</td><td>46–47</td><td>47–48</td><td>48–49</td><td>49–50</td><td>50–51</td></tr>
              </tbody>
            </table>
            <h3 style="margin-top:2em;">{{bra_size_cal_chart_cm}}</h3>
            <table class="bmi-ranges-table" aria-label="{{bra_size_cal_chart_cm_aria}}">
              <thead>
                <tr>
                  <th>{{bra_size_cal_band_size}}</th>
                  <th>{{bra_size_cal_under_bust_cm}}</th>
                  <th>{{bra_size_cal_over_bust_a}}</th>
                  <th>{{bra_size_cal_over_bust_b}}</th>
                  <th>{{bra_size_cal_over_bust_c}}</th>
                  <th>{{bra_size_cal_over_bust_d}}</th>
                  <th>{{bra_size_cal_over_bust_dd}}</th>
                  <th>{{bra_size_cal_over_bust_f}}</th>
                  <th>{{bra_size_cal_over_bust_g}}</th>
                </tr>
              </thead>
              <tbody>
                <tr><td>28</td><td>58.5–61</td><td>71.5–74</td><td>74–76.5</td><td>76.5–79</td><td>79–81.5</td><td>81.5–84</td><td>84–86.5</td><td>86.5–89</td></tr>
                <tr><td>30</td><td>63.5–66</td><td>76.5–79</td><td>79–81.5</td><td>81.5–84</td><td>84–86.5</td><td>86.5–89</td><td>89–91.5</td><td>91.5–94</td></tr>
                <tr><td>32</td><td>68.5–71</td><td>81.5–84</td><td>84–86.5</td><td>86.5–89</td><td>89–91.5</td><td>91.5–94</td><td>94–96.5</td><td>96.5–99</td></tr>
                <tr><td>34</td><td>73.5–76</td><td>86.5–89</td><td>89–91.5</td><td>91.5–94</td><td>94–96.5</td><td>96.5–99</td><td>99–101.5</td><td>101.5–104</td></tr>
                <tr><td>36</td><td>78.5–81</td><td>91.5–94</td><td>94–96.5</td><td>96.5–99</td><td>99–101.5</td><td>101.5–104</td><td>104–106.5</td><td>106.5–109</td></tr>
                <tr><td>38</td><td>83.5–86</td><td>96.5–99</td><td>99–101.5</td><td>101.5–104</td><td>104–106.5</td><td>106.5–109</td><td>109–111.5</td><td>111.5–114</td></tr>
                <tr><td>40</td><td>88.5–91</td><td>101.5–104</td><td>104–106.5</td><td>106.5–109</td><td>109–111.5</td><td>111.5–114</td><td>114–116.5</td><td>116.5–119</td></tr>
                <tr><td>42</td><td>93.5–96</td><td>106.5–109</td><td>109–111.5</td><td>111.5–114</td><td>114–116.5</td><td>116.5–119</td><td>119–121.5</td><td>121.5–124</td></tr>
                <tr><td>44</td><td>98.5–101</td><td>111.5–114</td><td>114–116.5</td><td>116.5–119</td><td>119–121.5</td><td>121.5–124</td><td>124–126.5</td><td>126.5–129</td></tr>
              </tbody>
            </table>
            <div class="note-section">
              <p><strong>{{bra_size_cal_pro_tip}}</strong> {{bra_size_cal_pro_tip_text}}</p>
            </div>
          </div>
          <div class="calculator-section">
            <h2 class="calculator-title">{{bra_size_cal_find_bra_size}}</h2>
            <div class="unit-selector">
              <div class="unit-option">
                <input type="radio" id="in-units" name="units" value="in" checked />
                <label for="in-units">{{bra_size_cal_unit_in}}</label>
              </div>
              <div class="unit-option">
                <input type="radio" id="cm-units" name="units" value="cm" />
                <label for="cm-units">{{bra_size_cal_unit_cm}}</label>
              </div>
              <span class="reset-link" onclick="resetBraCalculator()">{{bra_size_cal_reset}}</span>
            </div>
            <div class="form-group">
              <label class="form-label">{{bra_size_cal_underbust}}</label>
              <div class="input-group" id="underbust-inputs">
                <input type="number" class="form-input" id="underbust" placeholder="0" min="0" />
                <span class="unit-label" id="underbust-unit">{{bra_size_cal_unit_in}}</span>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">{{bra_size_cal_overbust}}</label>
              <div class="input-group" id="overbust-inputs">
                <input type="number" class="form-input" id="overbust" placeholder="0" min="0" />
                <span class="unit-label" id="overbust-unit">{{bra_size_cal_unit_in}}</span>
              </div>
            </div>
            <button class="calculate-btn" onclick="calculateBraSize()">{{bra_size_cal_calculate}}</button>
            <div class="result-section" id="bra-result-section" style="display:none; margin-top:30px;">
              <div class="calculated-results" id="bra-size-result"></div>
            </div>
          </div>
        </div>
      </main>
    </div>
    <script src="script.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"{{bra_size_cal_lang_en}}",fr:"{{bra_size_cal_lang_fr}}",de:"{{bra_size_cal_lang_de}}",es:"{{bra_size_cal_lang_es}}",it:"{{bra_size_cal_lang_it}}",pt:"{{bra_size_cal_lang_pt}}",ru:"{{bra_size_cal_lang_ru}}",ja:"{{bra_size_cal_lang_ja}}",ko:"{{bra_size_cal_lang_ko}}",he:"{{bra_size_cal_lang_he}}",bg:"{{bra_size_cal_lang_bg}}",fi:"{{bra_size_cal_lang_fi}}",hr:"{{bra_size_cal_lang_hr}}",lv:"{{bra_size_cal_lang_lv}}",mk:"{{bra_size_cal_lang_mk}}",mr:"{{bra_size_cal_lang_mr}}",sk:"{{bra_size_cal_lang_sk}}",sl:"{{bra_size_cal_lang_sl}}",sr:"{{bra_size_cal_lang_sr}}",tl:"{{bra_size_cal_lang_tl}}",el:"{{bra_size_cal_lang_el}}"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>