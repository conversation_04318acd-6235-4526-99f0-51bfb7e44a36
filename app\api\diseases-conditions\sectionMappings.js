
// Mapping of database columns to section headings
const sectionMappings = {
    en: {
        overview: "Overview",
        symptoms: "Symptoms",
        when_to_see_doctor: "When to see a doctor",
        causes: "Causes",
        risk_factors: "Risk factors",
        complications: "Complications",
        prevention: "Prevention",
        diagnosis: "Diagnosis",
        treatment: "Treatment",
        self_care: "Self-care",
        preparing_for_your_appointment: "Preparing for your appointment"
    },
    es: {
        overview: "Descripción general",
        symptoms: "Síntomas",
        when_to_see_doctor: "Cuándo consultar al médico",
        causes: "Causas",
        risk_factors: "Factores de riesgo",
        complications: "Complicaciones",
        prevention: "Prevención",
        diagnosis: "Diagnóstico",
        treatment: "Tratamiento",
        self_care: "Autocuidado",
        preparing_for_your_appointment: "Preparación para su cita"
    },
    fr: {
        overview: "Aperçu",
        symptoms: "Symptômes",
        when_to_see_doctor: "Quand consulter un médecin",
        causes: "Causes",
        risk_factors: "Facteurs de risque",
        complications: "Complications",
        prevention: "Prévention",
        diagnosis: "Diagnostic",
        treatment: "Traitement",
         self_care: "Autocuidado",
        preparing_for_your_appointment: "Préparation pour votre rendez-vous"
    },
     ta: {
        overview: "கண்ணோட்டம்",
        symptoms: "அறிகுறிகள்",
       when_to_see_doctor: "மருத்துவரை எப்போது பார்க்க வேண்டும்",
        causes: "காரணங்கள்",
        risk_factors: "ஆபத்து காரணிகள்",
        complications: "சிக்கல்கள்",
        prevention: "தடுப்பு",
        diagnosis: "நோயறிதல்",
        treatment: "சிகிச்சை",
         self_care: "சுய பாதுகாப்பு",
        preparing_for_your_appointment: "உங்கள் சந்திப்புக்கு தயாராகுதல்"
    },
    de: {
        overview: "Überblick",
        symptoms: "Symptome",
       when_to_see_doctor: "Wann Sie einen Arzt aufsuchen sollten",
        causes: "Ursachen",
        risk_factors: "Risikofaktoren",
        complications: "Komplikationen",
        prevention: "Prävention",
        diagnosis: "Diagnose",
        treatment: "Behandlung",
         self_care: "Selbstpflege",
        preparing_for_your_appointment: "Vorbereitung auf Ihren Termin"
    },
   it: {
        overview: "Panoramica",
        symptoms: "Sintomi",
       when_to_see_doctor: "Quando consultare un medico",
       causes: "Cause",
        risk_factors: "Fattori di rischio",
        complications: "Complicazioni",
        prevention: "Prevenzione",
        diagnosis: "Diagnosi",
        treatment: "Trattamento",
         self_care: "Autocura",
        preparing_for_your_appointment: "Preparazione per l'appuntamento"
    },
    pt: {
        overview: "Visão geral",
        symptoms: "Sintomas",
       when_to_see_doctor: "Quando consultar um médico",
        causes: "Causas",
        risk_factors: "Fatores de risco",
        complications: "Complicações",
        prevention: "Prevenção",
        diagnosis: "Diagnóstico",
        treatment: "Tratamento",
         self_care: "Autocuidado",
        preparing_for_your_appointment: "Preparação para a sua consulta"
    },
    ru: {
        overview: "Обзор",
       symptoms: "Симптомы",
       when_to_see_doctor: "Когда обращаться к врачу",
       causes: "Причины",
        risk_factors: "Факторы риска",
        complications: "Осложнения",
        prevention: "Профилактика",
        diagnosis: "Диагностика",
        treatment: "Лечение",
        self_care: "Самопомощь",
         preparing_for_your_appointment: "Подготовка к приему"
    },
    'zh-Hans': {
        overview: "概述",
        symptoms: "症状",
       when_to_see_doctor: "何时去看医生",
        causes: "病因",
       risk_factors: "风险因素",
       complications: "并发症",
        prevention: "预防",
        diagnosis: "诊断",
        treatment: "治疗",
        self_care: "自我护理",
        preparing_for_your_appointment: "为您的预约做准备"
    },
    'zh-Hant': {
        overview: "概述",
        symptoms: "症狀",
       when_to_see_doctor: "何時去看醫生",
        causes: "病因",
        risk_factors: "風險因素",
       complications: "併發症",
       prevention: "預防",
        diagnosis: "診斷",
        treatment: "治療",
         self_care: "自我護理",
        preparing_for_your_appointment: "為您的預約做準備"
    },
    ja: {
        overview: "概要",
        symptoms: "症状",
       when_to_see_doctor: "医師に相談するタイミング",
        causes: "原因",
        risk_factors: "リスク要因",
       complications: "合併症",
        prevention: "予防",
       diagnosis: "診断",
        treatment: "治療",
        self_care: "セルフケア",
       preparing_for_your_appointment: "診察の準備"
    },
    ko: {
       overview: "개요",
        symptoms: "증상",
       when_to_see_doctor: "의사를 만나야 할 때",
        causes: "원인",
        risk_factors: "위험 요인",
        complications: "합병증",
        prevention: "예방",
        diagnosis: "진단",
        treatment: "치료",
        self_care: "자가 관리",
        preparing_for_your_appointment: "진료 준비"
    },
    ar: {
       overview: "نظرة عامة",
        symptoms: "الأعراض",
        when_to_see_doctor: "متى ترى الطبيب",
        causes: "الأسباب",
        risk_factors: "عوامل الخطر",
       complications: "المضاعفات",
       prevention: "الوقاية",
        diagnosis: "التشخيص",
        treatment: "العلاج",
        self_care: "الرعاية الذاتية",
       preparing_for_your_appointment: "التحضير لموعدك"
    },
    hi: {
       overview: "अवलोकन",
        symptoms: "लक्षण",
      when_to_see_doctor: "डॉक्टर को कब दिखाना है",
        causes: "कारण",
       risk_factors: "जोखिम कारक",
       complications: "जटिलताएँ",
        prevention: "रोकथाम",
      diagnosis: "निदान",
       treatment: "उपचार",
        self_care: "स्वयं देखभाल",
       preparing_for_your_appointment: "अपनी अपॉइंटमेंट की तैयारी"
    },
     nl: {
        overview: "Overzicht",
        symptoms: "Symptomen",
        when_to_see_doctor: "Wanneer een arts raadplegen",
       causes: "Oorzaken",
        risk_factors: "Risicofactoren",
       complications: "Complicaties",
       prevention: "Preventie",
       diagnosis: "Diagnose",
        treatment: "Behandeling",
       self_care: "Zelfzorg",
        preparing_for_your_appointment: "Voorbereiding op uw afspraak"
    },
   pl: {
        overview: "Przegląd",
       symptoms: "Objawy",
       when_to_see_doctor: "Kiedy udać się do lekarza",
        causes: "Przyczyny",
        risk_factors: "Czynniki ryzyka",
        complications: "Powikłania",
        prevention: "Profilaktyka",
       diagnosis: "Diagnoza",
        treatment: "Leczenie",
       self_care: "Samoopieka",
       preparing_for_your_appointment: "Przygotowanie do wizyty"
    },
    sv: {
        overview: "Översikt",
       symptoms: "Symptom",
       when_to_see_doctor: "När ska man söka läkare",
      causes: "Orsaker",
        risk_factors: "Riskfaktorer",
       complications: "Komplikationer",
        prevention: "Förebyggande",
        diagnosis: "Diagnos",
        treatment: "Behandling",
        self_care: "Egenvård",
       preparing_for_your_appointment: "Förberedelse inför ditt besök"
    },
    no: {
       overview: "Oversikt",
        symptoms: "Symptomer",
       when_to_see_doctor: "Når bør du oppsøke lege",
        causes: "Årsaker",
       risk_factors: "Risikofaktorer",
        complications: "Komplikasjoner",
        prevention: "Forebygging",
        diagnosis: "Diagnose",
       treatment: "Behandling",
        self_care: "Egenomsorg",
        preparing_for_your_appointment: "Forberedelse til timen din"
    },
    da: {
        overview: "Oversigt",
        symptoms: "Symptomer",
       when_to_see_doctor: "Hvornår skal man søge læge",
        causes: "Årsager",
        risk_factors: "Risikofaktorer",
        complications: "Komplikationer",
        prevention: "Forebyggelse",
       diagnosis: "Diagnose",
        treatment: "Behandling",
        self_care: "Selvpleje",
       preparing_for_your_appointment: "Forberedelse til din aftale"
    },
   fi: {
      overview: "Yleiskatsaus",
        symptoms: "Oireet",
       when_to_see_doctor: "Milloin lääkäriin",
        causes: "Syyt",
        risk_factors: "Riskitekijät",
        complications: "Komplikaatiot",
        prevention: "Ehkäisy",
        diagnosis: "Diagnoosi",
        treatment: "Hoito",
       self_care: "Itsehoito",
        preparing_for_your_appointment: "Valmistautuminen tapaamiseen"
    },
    cs: {
        overview: "Přehled",
        symptoms: "Příznaky",
       when_to_see_doctor: "Kdy navštívit lékaře",
      causes: "Příčiny",
        risk_factors: "Rizikové faktory",
       complications: "Komplikace",
        prevention: "Prevence",
        diagnosis: "Diagnóza",
        treatment: "Léčba",
        self_care: "Samoléčba",
       preparing_for_your_appointment: "Příprava na Vaši schůzku"
    },
    hu: {
        overview: "Áttekintés",
        symptoms: "Tünetek",
        when_to_see_doctor: "Mikor forduljon orvoshoz",
        causes: "Okok",
       risk_factors: "Kockázati tényezők",
        complications: "Szövődmények",
        prevention: "Megelőzés",
        diagnosis: "Diagnózis",
        treatment: "Kezelés",
        self_care: "Öngondoskodás",
        preparing_for_your_appointment: "Felkészülés a találkozóra"
    },
    ro: {
        overview: "Prezentare generală",
       symptoms: "Simptome",
        when_to_see_doctor: "Când să consultați un medic",
        causes: "Cauze",
       risk_factors: "Factori de risc",
       complications: "Complicații",
        prevention: "Prevenție",
        diagnosis: "Diagnostic",
        treatment: "Tratament",
        self_care: "Autoîngrijire",
        preparing_for_your_appointment: "Pregătirea pentru programare"
    },
     el: {
       overview: "Επισκόπηση",
        symptoms: "Συμπτώματα",
       when_to_see_doctor: "Πότε να επισκεφθείτε έναν γιατρό",
        causes: "Αιτίες",
       risk_factors: "Παράγοντες κινδύνου",
        complications: "Επιπλοκές",
       prevention: "Πρόληψη",
       diagnosis: "Διάγνωση",
       treatment: "Θεραπεία",
        self_care: "Αυτοφροντίδα",
        preparing_for_your_appointment: "Προετοιμασία για το ραντεβού σας"
    },
    uk: {
        overview: "Огляд",
        symptoms: "Симптоми",
        when_to_see_doctor: "Коли звертатися до лікаря",
        causes: "Причини",
        risk_factors: "Фактори ризику",
        complications: "Ускладнення",
       prevention: "Профілактика",
        diagnosis: "Діагностика",
        treatment: "Лікування",
        self_care: "Самодопомога",
      preparing_for_your_appointment: "Підготовка до прийому"
    },
     bg: {
        overview: "Общ преглед",
        symptoms: "Симптоми",
        when_to_see_doctor: "Кога да посетите лекар",
        causes: "Причини",
       risk_factors: "Рискови фактори",
        complications: "Усложнения",
        prevention: "Профилактика",
        diagnosis: "Диагноза",
        treatment: "Лечение",
         self_care: "Самопомощ",
        preparing_for_your_appointment: "Подготовка за прегледа"
    },
   hr: {
      overview: "Pregled",
        symptoms: "Simptomi",
       when_to_see_doctor: "Kada posjetiti liječnika",
        causes: "Uzroci",
       risk_factors: "Čimbenici rizika",
       complications: "Komplikacije",
       prevention: "Prevencija",
      diagnosis: "Dijagnoza",
        treatment: "Liječenje",
         self_care: "Samopomoć",
         preparing_for_your_appointment: "Priprema za vaš termin"
    },
   sk: {
        overview: "Prehľad",
        symptoms: "Príznaky",
       when_to_see_doctor: "Kedy navštíviť lekára",
       causes: "Príčiny",
       risk_factors: "Rizikové faktory",
      complications: "Komplikácie",
       prevention: "Prevencia",
        diagnosis: "Diagnóza",
        treatment: "Liečba",
         self_care: "Samoliečba",
       preparing_for_your_appointment: "Príprava na Vaše vyšetrenie"
    },
   sl: {
        overview: "Pregled",
        symptoms: "Simptomi",
       when_to_see_doctor: "Kdaj k zdravniku",
       causes: "Vzroki",
       risk_factors: "Dejavniki tveganja",
       complications: "Zapleti",
       prevention: "Preprečevanje",
        diagnosis: "Diagnoza",
       treatment: "Zdravljenje",
        self_care: "Samopomoč",
       preparing_for_your_appointment: "Priprava na vaš termin"
    },
    et: {
       overview: "Ülevaade",
       symptoms: "Sümptomid",
       when_to_see_doctor: "Millal arsti poole pöörduda",
        causes: "Põhjused",
       risk_factors: "Riskifaktorid",
       complications: "Tüsistused",
        prevention: "Ennetamine",
       diagnosis: "Diagnoos",
        treatment: "Ravi",
        self_care: "Enesehooldus",
        preparing_for_your_appointment: "Kohtumiseks valmistumine"
    },
    lv: {
       overview: "Pārskats",
        symptoms: "Simptomi",
       when_to_see_doctor: "Kad apmeklēt ārstu",
       causes: "Cēloņi",
       risk_factors: "Riska faktori",
        complications: "Komplikācijas",
       prevention: "Profilakse",
       diagnosis: "Diagnoze",
       treatment: "Ārstēšana",
        self_care: "Pašaprūpe",
       preparing_for_your_appointment: "Gatavošanās vizītei"
    },
    lt: {
        overview: "Apžvalga",
        symptoms: "Simptomai",
        when_to_see_doctor: "Kada kreiptis į gydytoją",
       causes: "Priežastys",
       risk_factors: "Rizikos veiksniai",
       complications: "Komplikacijos",
       prevention: "Prevencija",
       diagnosis: "Diagnozė",
        treatment: "Gydymas",
         self_care: "Savigyda",
        preparing_for_your_appointment: "Pasiruošimas jūsų vizitui"
    },
    is: {
        overview: "Yfirlit",
        symptoms: "Einkenni",
        when_to_see_doctor: "Hvenær skal leita til læknis",
       causes: "Orsakir",
        risk_factors: "Áhættuþættir",
       complications: "Fylgikvillar",
        prevention: "Forvarnir",
        diagnosis: "Greining",
        treatment: "Meðferð",
         self_care: "Sjálfsumönnun",
         preparing_for_your_appointment: "Undirbúningur fyrir tíma"
    },
    ga: {
        overview: "Forléargas",
       symptoms: "Siomptóim",
       when_to_see_doctor: "Cathain chun dochtúir a fheiceáil",
       causes: "Cúiseanna",
       risk_factors: "Fachtóirí riosca",
        complications: "Deacrachtaí",
        prevention: "Cosc",
      diagnosis: "Diagnóis",
       treatment: "Cóireáil",
        self_care: "Féinchúram",
      preparing_for_your_appointment: "Ullmhúchán don choinne agat"
    },
   mt: {
        overview: "Ħarsa ġenerali",
        symptoms: "Sintomi",
      when_to_see_doctor: "Meta għandek tara tabib",
      causes: "Kawżi",
        risk_factors: "Fatturi ta’ riskju",
       complications: "Kumplikazzjonijiet",
      prevention: "Prevenzjoni",
        diagnosis: "Dijanjosi",
        treatment: "Trattament",
         self_care: "Kura personali",
       preparing_for_your_appointment: "Tħejjija għall-appuntament tiegħek"
    },
    sq: {
       overview: "Përmbledhje",
        symptoms: "Simptomat",
       when_to_see_doctor: "Kur të shkoni te mjeku",
       causes: "Shkaqet",
      risk_factors: "Faktorët e rrezikut",
      complications: "Komplikacionet",
        prevention: "Parandalimi",
      diagnosis: "Diagnoza",
        treatment: "Trajtimi",
         self_care: "Vetëkujdesi",
       preparing_for_your_appointment: "Përgatitja për takimin tuaj"
    },
   be: {
       overview: "Агляд",
       symptoms: "Сімптомы",
        when_to_see_doctor: "Калі звяртацца да ўрача",
        causes: "Прычыны",
        risk_factors: "Фактары рызыкі",
        complications: "Ускладненні",
        prevention: "Прафілактыка",
        diagnosis: "Дыягностыка",
        treatment: "Лячэнне",
        self_care: "Самадапамога",
         preparing_for_your_appointment: "Падрыхтоўка да прыёму"
    },
    bs: {
      overview: "Pregled",
        symptoms: "Simptomi",
       when_to_see_doctor: "Kada posjetiti liječnika",
        causes: "Uzroci",
       risk_factors: "Faktori rizika",
       complications: "Komplikacije",
       prevention: "Prevencija",
       diagnosis: "Dijagnoza",
        treatment: "Liječenje",
         self_care: "Samopomoć",
        preparing_for_your_appointment: "Priprema za vaš termin"
    },
    gd: {
      overview: "Sealladh farsaing",
        symptoms: "Comharran",
        when_to_see_doctor: "Cuin a bu chòir dotair fhaicinn",
       causes: "Adhbharan",
        risk_factors: "Factaran cunnairt",
        complications: "Duilgheadasan",
        prevention: "Casg",
        diagnosis: "Breithneachadh",
       treatment: "Làimhseachadh",
        self_care: "Fèin-chùram",
       preparing_for_your_appointment: "Ag ullachadh airson do choinneamh"
    },
    lb: {
        overview: "Iwwerbléck",
        symptoms: "Symptomer",
       when_to_see_doctor: "Wéini soll een Dokter konsultéieren",
        causes: "Ursaachen",
        risk_factors: "Risikofaktoren",
       complications: "Komplikatiounen",
        prevention: "Préventioun",
        diagnosis: "Diagnos",
        treatment: "Behandlung",
        self_care: "Selbstfleeg",
         preparing_for_your_appointment: "Virbereedung fir Äre Rendez-vous"
    },
    mk: {
        overview: "Преглед",
        symptoms: "Симптоми",
       when_to_see_doctor: "Кога да посетите лекар",
        causes: "Причини",
        risk_factors: "Фактори на ризик",
       complications: "Компликации",
       prevention: "Превенција",
        diagnosis: "Дијагноза",
        treatment: "Третман",
       self_care: "Самогрижа",
        preparing_for_your_appointment: "Подготовка за вашиот преглед"
    },
   sr: {
        overview: "Преглед",
        symptoms: "Симптоми",
       when_to_see_doctor: "Када посетити лекара",
       causes: "Узроци",
        risk_factors: "Фактори ризика",
        complications: "Компликације",
       prevention: "Превенција",
       diagnosis: "Дијагноза",
        treatment: "Лечење",
        self_care: "Самопомоћ",
        preparing_for_your_appointment: "Припрема за ваш термин"
    },
    cy: {
       overview: "Trosolwg",
       symptoms: "Symptomau",
      when_to_see_doctor: "Pryd i weld meddyg",
        causes: "Achosion",
       risk_factors: "Ffactorau risg",
       complications: "Cymhlethdodau",
        prevention: "Atal",
       diagnosis: "Diagnosis",
       treatment: "Triniaeth",
       self_care: "Hunanofal",
        preparing_for_your_appointment: "Paratoi ar gyfer eich apwyntiad"
    },
    vi: {
       overview: "Tổng quan",
        symptoms: "Triệu chứng",
        when_to_see_doctor: "Khi nào cần gặp bác sĩ",
       causes: "Nguyên nhân",
       risk_factors: "Yếu tố rủi ro",
       complications: "Biến chứng",
        prevention: "Phòng ngừa",
        diagnosis: "Chẩn đoán",
        treatment: "Điều trị",
         self_care: "Tự chăm sóc",
         preparing_for_your_appointment: "Chuẩn bị cho cuộc hẹn của bạn"
    },
    th: {
       overview: "ภาพรวม",
        symptoms: "อาการ",
      when_to_see_doctor: "เมื่อไหร่ควรไปพบแพทย์",
        causes: "สาเหตุ",
        risk_factors: "ปัจจัยเสี่ยง",
        complications: "ภาวะแทรกซ้อน",
        prevention: "การป้องกัน",
       diagnosis: "การวินิจฉัย",
        treatment: "การรักษา",
         self_care: "การดูแลตนเอง",
        preparing_for_your_appointment: "การเตรียมตัวสำหรับการนัดหมายของคุณ"
    },
     id: {
       overview: "Gambaran Umum",
        symptoms: "Gejala",
        when_to_see_doctor: "Kapan harus menemui dokter",
        causes: "Penyebab",
        risk_factors: "Faktor risiko",
        complications: "Komplikasi",
       prevention: "Pencegahan",
        diagnosis: "Diagnosis",
        treatment: "Pengobatan",
        self_care: "Perawatan diri",
        preparing_for_your_appointment: "Persiapan untuk janji temu Anda"
    },
    ms: {
        overview: "Gambaran Keseluruhan",
       symptoms: "Gejala",
        when_to_see_doctor: "Bila perlu berjumpa doktor",
        causes: "Punca",
       risk_factors: "Faktor risiko",
        complications: "Komplikasi",
       prevention: "Pencegahan",
      diagnosis: "Diagnosis",
        treatment: "Rawatan",
         self_care: "Penjagaan diri",
       preparing_for_your_appointment: "Persediaan untuk temujanji anda"
    },
    tl: {
        overview: "Pangkalahatang-ideya",
      symptoms: "Mga Sintomas",
       when_to_see_doctor: "Kailan dapat magpatingin sa doktor",
        causes: "Mga Sanhi",
      risk_factors: "Mga Salik ng Panganib",
       complications: "Mga Komplikasyon",
       prevention: "Pag-iwas",
        diagnosis: "Diagnosis",
        treatment: "Paggamot",
        self_care: "Pangangalaga sa Sarili",
        preparing_for_your_appointment: "Paghahanda para sa iyong appointment"
    },
     bn: {
      overview: "সংক্ষিপ্ত বিবরণ",
        symptoms: "লক্ষণ",
        when_to_see_doctor: "কখন ডাক্তার দেখাবেন",
       causes: "কারণ",
        risk_factors: "ঝুঁকির কারণ",
        complications: "জটিলতা",
        prevention: "প্রতিরোধ",
       diagnosis: "রোগ নির্ণয়",
        treatment: "চিকিৎসা",
       self_care: "স্ব-যত্ন",
        preparing_for_your_appointment: "আপনার অ্যাপয়েন্টমেন্টের জন্য প্রস্তুতি"
    },
    ur: {
        overview: "جائزہ",
       symptoms: "علامات",
        when_to_see_doctor: "ڈاکٹر کو کب دکھانا ہے",
        causes: "اسباب",
        risk_factors: "خطرے کے عوامل",
        complications: "پیچیدگیاں",
        prevention: "احتیاط",
       diagnosis: "تشخیص",
        treatment: "علاج",
         self_care: "خود کی دیکھ بھال",
       preparing_for_your_appointment: "اپنے اپائنٹمنٹ کی تیاری"
    },
    te: {
      overview: "సారాంశం",
        symptoms: "లక్షణాలు",
      when_to_see_doctor: "వైద్యుడిని ఎప్పుడు కలవాలి",
        causes: "కారణాలు",
       risk_factors: "ప్రమాద కారకాలు",
      complications: "సమస్యలు",
      prevention: "నివారణ",
        diagnosis: "రోగ నిర్ధారణ",
        treatment: "చికిత్స",
         self_care: "స్వీయ సంరక్షణ",
      preparing_for_your_appointment: "మీ అపాయింట్‌మెంట్ కోసం సిద్ధమవుతోంది"
    },
    mr: {
       overview: "आढावा",
        symptoms: "लक्षणे",
       when_to_see_doctor: "डॉक्टरांना कधी भेटावे",
        causes: "कारणे",
       risk_factors: "जोखिम घटक",
        complications: "गुंतागुंत",
        prevention: "प्रतिबंध",
      diagnosis: "निदान",
       treatment: "उपचार",
        self_care: "स्वतःची काळजी",
        preparing_for_your_appointment: "तुमच्या भेटीसाठी तयारी"
    },
    gu: {
       overview: "સંક્ષિપ્ત ઝાંખી",
        symptoms: "ચિહ્નો",
       when_to_see_doctor: "ડોક્ટરને ક્યારે મળવું",
       causes: "કારણો",
        risk_factors: "જોખમ પરિબળો",
        complications: "ગૂંચવણો",
        prevention: "નિવારણ",
        diagnosis: "નિદાન",
        treatment: "સારવાર",
        self_care: "સ્વ-સંભાળ",
        preparing_for_your_appointment: "તમારી એપોઇન્ટમેન્ટની તૈયારી"
    },
     kn: {
       overview: "ಸಾರಾಂಶ",
        symptoms: "ಲಕ್ಷಣಗಳು",
       when_to_see_doctor: "ವೈದ್ಯರನ್ನು ಯಾವಾಗ ಭೇಟಿ ಮಾಡಬೇಕು",
       causes: "ಕಾರಣಗಳು",
        risk_factors: "ಅಪಾಯಕಾರಿ ಅಂಶಗಳು",
       complications: "ಸಂಕೀರ್ಣತೆಗಳು",
        prevention: "ತಡೆಗಟ್ಟುವಿಕೆ",
       diagnosis: "ರೋಗನಿರ್ಣಯ",
        treatment: "ಚಿಕಿತ್ಸೆ",
        self_care: "ಸ್ವಯಂ ಆರೈಕೆ",
       preparing_for_your_appointment: "ನಿಮ್ಮ ಅಪಾಯಿಂಟ್‌ಮೆಂಟ್‌ಗೆ ಸಿದ್ಧತೆ"
    },
    pa: {
      overview: "ਸੰਖੇਪ ਜਾਣਕਾਰੀ",
        symptoms: "ਲੱਛਣ",
        when_to_see_doctor: "ਡਾਕਟਰ ਕੋਲ ਕਦੋਂ ਜਾਣਾ ਹੈ",
       causes: "ਕਾਰਨ",
        risk_factors: "ਜੋਖਮ ਦੇ ਕਾਰਕ",
        complications: "ਪੇਚੀਦਗੀਆਂ",
       prevention: "ਰੋਕਥਾਮ",
        diagnosis: "ਨਿਦਾਨ",
       treatment: "ਇਲਾਜ",
        self_care: "ਆਪਣੀ ਦੇਖਭਾਲ",
      preparing_for_your_appointment: "ਆਪਣੀ ਮੁਲਾਕਾਤ ਦੀ ਤਿਆਰੀ ਕਰਨਾ"
    },
     ne: {
       overview: "सिंहावलोकन",
        symptoms: "लक्षणहरू",
       when_to_see_doctor: "डाक्टरलाई कहिले देखाउने",
        causes: "कारणहरू",
        risk_factors: "जोखिम कारकहरू",
       complications: "जटिलताहरू",
       prevention: "रोकथाम",
        diagnosis: "निदान",
        treatment: "उपचार",
        self_care: "आत्म-हेरचाह",
         preparing_for_your_appointment: "तपाईंको भेटको लागि तयारी गर्दै"
    },
      my: {
       overview: "အကျဉ်းချုပ်",
        symptoms: "ရောဂါလက္ခဏာများ",
        when_to_see_doctor: "ဘယ်အချိန်မှာ ဆရာဝန်နဲ့ ပြသသင့်လဲ",
        causes: "အကြောင်းရင်းများ",
        risk_factors: "အန္တရာယ်ရှိသောအချက်များ",
        complications: "ရှုပ်ထွေးမှုများ",
        prevention: "ကာကွယ်ခြင်း",
       diagnosis: "ရောဂါရှာဖွေခြင်း",
        treatment: "ကုသမှု",
        self_care: "မိမိကိုယ်ကို ဂရုစိုက်ခြင်း",
        preparing_for_your_appointment: "သင်၏ ရက်ချိန်းအတွက် ပြင်ဆင်ခြင်း"
    },
    km: {
       overview: "ទិដ្ឋភាពទូទៅ",
        symptoms: "រោគសញ្ញា",
        when_to_see_doctor: "ពេលណាត្រូវជួបគ្រូពេទ្យ",
        causes: "មូលហេតុ",
        risk_factors: "កត្តាហានិភ័យ",
       complications: "ផលវិបាក",
        prevention: "ការការពារ",
       diagnosis: "ការធ្វើរោគវិនិច្ឆ័យ",
       treatment: "ការព្យាបាល",
        self_care: "ការថែទាំខ្លួនឯង",
        preparing_for_your_appointment: "ការរៀបចំសម្រាប់ការណាត់ជួបរបស់អ្នក"
    },
    si: {
      overview: 'දළ විශ්ලේෂණය',
          symptoms: 'රෝග ලක්ෂණ',
          when_to_see_doctor: 'වෛද්‍යවරයකු හමුවිය යුත්තේ කවදාද',
          causes: 'හේතු',
          risk_factors: 'අවදානම් සාධක',
          complications: 'සංකූලතා',
          prevention: 'වැළැක්වීම',
          diagnosis: 'රෝග විනිශ්චය',
          treatment: 'ප්රතිකාර',
          self_care: 'ස්වයං රැකවරණය',
          preparing_for_your_appointment: 'ඔබේ හමුවීමට සූදානම් වීම',
    },
    ml: {
        overview: 'അവലോകനം',
          symptoms: 'ലക്ഷണങ്ങൾ',
          when_to_see_doctor: 'ഡോക്ടറെ എപ്പോൾ കാണണം',
          causes: 'കാരണങ്ങൾ',
          risk_factors: 'അപകട ഘടകങ്ങൾ',
          complications: 'സങ്കീർണതകൾ',
          prevention: 'പ്രതിരോധം',
          diagnosis: 'രോഗനിര്ണയം',
          treatment: 'ചികിത്സ',
          self_care: 'സ്വയം പരിചരണം',
          preparing_for_your_appointment: 'നിങ്ങളുടെ അപ്പോയിന്റ്മെന്റിനായി തയ്യാറെടുക്കുന്നു',
    },
    mn: {
      overview: 'Тойм',
      symptoms: 'Шинж тэмдгүүд',
      when_to_see_doctor: 'Хэзээ эмчид үзүүлэх вэ',
      causes: 'Шалтгаанууд',
      risk_factors: 'Эрсдэлт хүчин зүйлс',
      complications: 'Хүндрэлүүд',
      prevention: 'Урьдчилан сэргийлэх',
      diagnosis: 'Оношилгоо',
      treatment: 'Эмчилгээ',
      self_care: 'Өөртөө анхаарал тавих',
      preparing_for_your_appointment: 'Эмчид үзүүлэхэд бэлтгэх',
    },
      jv: {
         overview: 'Ringkesan',
        symptoms: 'Gejala',
          when_to_see_doctor: 'Nalika golek dhokter',
          causes: 'Panyebab',
          risk_factors: 'Faktor risiko',
          complications: 'Komplikasi',
          prevention: 'Pencegahan',
         diagnosis: 'Diagnosis',
          treatment: 'Perawatan',
         self_care: 'Perawatan diri',
          preparing_for_your_appointment: 'Nyiapake kanggo janjian sampeyan',
    },
    su: {
         overview: 'Tinjauan',
        symptoms: 'Gejala',
          when_to_see_doctor: 'Ira ningali dokter',
          causes: 'Sabab',
        risk_factors: 'Faktor résiko',
          complications: 'Komplikasi',
        prevention: 'Pencegahan',
          diagnosis: 'Diagnosis',
         treatment: 'Perawatan',
         self_care: 'Pangobatan diri',
          preparing_for_your_appointment: 'Nyiapkeun kanggo janji anjeun',
    },
    sw: {
          overview: 'Muhtasari',
         symptoms: 'Dalili',
          when_to_see_doctor: 'Wakati wa kuona daktari',
          causes: 'Sababu',
          risk_factors: 'Sababu za hatari',
          complications: 'Matatizo',
        prevention: 'Kinga',
         diagnosis: 'Utambuzi',
         treatment: 'Matibabu',
          self_care: 'Kujitunza',
          preparing_for_your_appointment: 'Kujiandaa kwa miadi yako',
      },
      he: {
         overview: 'סקירה כללית',
        symptoms: 'תסמינים',
        when_to_see_doctor: 'מתי לפנות לרופא',
          causes: 'סיבות',
        risk_factors: 'גורמי סיכון',
          complications: 'סיבוכים',
          prevention: 'מניעה',
         diagnosis: 'אבחון',
          treatment: 'טיפול',
          self_care: 'טיפול עצמי',
          preparing_for_your_appointment: 'הכנה לפגישה שלך',
    },
    fa: {
      overview: 'بررسی اجمالی',
          symptoms: 'علائم',
          when_to_see_doctor: 'چه زمانی به پزشک مراجعه کنیم',
        causes: 'علل',
          risk_factors: 'عوامل خطر',
         complications: 'عوارض',
          prevention: 'پیشگیری',
         diagnosis: 'تشخیص',
          treatment: 'درمان',
          self_care: 'مراقبت از خود',
         preparing_for_your_appointment: 'آماده شدن برای نوبت خود',
      },
      tr: {
          overview: 'Genel Bakış',
         symptoms: 'Belirtiler',
          when_to_see_doctor: 'Ne zaman doktora görünmeli',
         causes: 'Nedenler',
          risk_factors: 'Risk faktörleri',
        complications: 'Komplikasyonlar',
          prevention: 'Önleme',
        diagnosis: 'Teşhis',
          treatment: 'Tedavi',
         self_care: 'Öz bakım',
         preparing_for_your_appointment: 'Randevunuza hazırlanma',
      },
      af: {
          overview: 'Oorsig',
          symptoms: 'Simptome',
          when_to_see_doctor: 'Wanneer om dokter toe te gaan',
          causes: 'Oorsake',
        risk_factors: 'Risikofaktore',
         complications: 'Komplikasies',
          prevention: 'Voorkoming',
          diagnosis: 'Diagnose',
          treatment: 'Behandeling',
          self_care: 'Selfversorging',
          preparing_for_your_appointment: 'Voorbereiding vir jou afspraak',
      },
     am: {
        overview: 'አጠቃላይ እይታ',
        symptoms: 'ምልክቶች',
       when_to_see_doctor: 'ዶክተር መቼ ማየት እንዳለብዎት',
         causes: 'ምክንያቶች',
        risk_factors: 'የአደጋ ምክንያቶች',
         complications: 'ችግሮች',
        prevention: 'መከላከል',
         diagnosis: 'ምርመራ',
          treatment: 'ሕክምና',
         self_care: 'ራስን መንከባከብ',
          preparing_for_your_appointment: 'ለቀጠሮዎ መዘጋጀት',
      },
      so: {
          overview: 'Guudmar',
        symptoms: 'Calaamadaha',
         when_to_see_doctor: 'Goorma la arko dhakhtar',
          causes: 'Sababaha',
          risk_factors: 'Qodobada halista',
         complications: 'Dhibaatooyinka',
         prevention: 'Ka hortagga',
         diagnosis: 'Cilad-sheegid',
         treatment: 'Daaweyn',
          self_care: 'Is-daryeel',
          preparing_for_your_appointment: 'U diyaargarowga ballantaada',
      },
      yo: {
        overview: 'Àkópọ̀',
        symptoms: 'Àwọn àmì',
         when_to_see_doctor: 'Ìgbà tí o yẹ kí o lọ sí ọ̀dọ̀ oníṣègùn',
          causes: 'Àwọn okùnfà',
         risk_factors: 'Àwọn okunfa ewu',
         complications: 'Àwọn ìṣòro',
          prevention: 'Ìdènà',
        diagnosis: 'Ayẹ̀wò àrùn',
          treatment: 'Ìtọ́jú',
        self_care: 'Itọju ara ẹni',
         preparing_for_your_appointment: 'Ṣíṣe ìmúrasílẹ̀ fún ìpàdé rẹ',
    },
    zu: {
          overview: 'Uhlolojikelele',
          symptoms: 'Izimpawu',
        when_to_see_doctor: 'Isikhathi sokubona udokotela',
          causes: 'Izimbangela',
       risk_factors: 'Izici eziyingozi',
        complications: 'Izingqinamba',
          prevention: 'Ukuvimbela',
        diagnosis: 'Ukuxilongwa',
          treatment: 'Ukwelashwa',
          self_care: 'Ukuzinakekela',
        preparing_for_your_appointment: 'Ukulungiselela ukuqokwa kwakho',
     },
      ha: {
          overview: 'Taƙaitaccen bayani',
          symptoms: 'Alamomi',
         when_to_see_doctor: 'Yaushe za a ga likita',
          causes: 'Dalilai',
       risk_factors: 'Abubuwan haɗari',
          complications: 'Matsaloli',
         prevention: 'Rigakafi',
         diagnosis: 'Gano asali',
          treatment: 'Jiyya',
        self_care: 'Kulawa da kai',
          preparing_for_your_appointment: 'Shiryawa don nadin ku',
      },
      ig: {
          overview: 'Nchịkọta',
          symptoms: 'Mgbaàmà',
         when_to_see_doctor: 'Oge ị ga-ahụ dọkịta',
         causes: 'Ihe na-akpata ya',
         risk_factors: 'Ihe ndị na-akpata ihe ize ndụ',
          complications: 'Nsogbu',
         prevention: 'Mgbochi',
         diagnosis: 'Nchoputa',
         treatment: 'Ọgwụgwọ',
          self_care: 'Nlekọta onwe',
        preparing_for_your_appointment: 'Ịkwado maka nhọpụta gị',
    },
      rw: {
          overview: 'Incamake',
          symptoms: 'Ibimenyetso',
         when_to_see_doctor: 'Igihe cyo kubona umuganga',
         causes: 'Impamvu',
        risk_factors: 'Ingaruka zishobora guteza',
          complications: 'Ingaruka',
       prevention: 'Kwirinda',
          diagnosis: 'Kupima',
         treatment: 'Uburyo bwo kuvura',
          self_care: 'Kwitaho',
        preparing_for_your_appointment: 'Kwitegura guhura na muganga',
    },
      om: {
          overview: 'Ibsaa',
        symptoms: 'Mallattoolee',
         when_to_see_doctor: 'Yeroo itti ogeessa fayyaa argitu',
          causes: 'Sababoota',
         risk_factors: 'Wantoota balaa fidan',
         complications: 'Rakkoolee',
          prevention: 'Ittisa',
        diagnosis: 'Sakatta’iinsa dhukkuba',
         treatment: 'Yaala',
          self_care: 'Of kunuunsuu',
          preparing_for_your_appointment: 'Qophii yeroo beellama keetii',
    },
      sn: {
          overview: 'Pfupiso',
          symptoms: 'Zviratidzo',
        when_to_see_doctor: 'Nguva yekunoona chiremba',
          causes: 'Zvinokonzera',
        risk_factors: 'Zvinhu zvinokonzera njodzi',
         complications: 'Matambudziko',
          prevention: 'Kudzivirira',
         diagnosis: 'Kuongorora',
          treatment: 'Kurapa',
         self_care: 'Kuzvitarisira',
          preparing_for_your_appointment: 'Kugadzirira musangano wako',
      },
       ht: {
          overview: 'Apèsi',
         symptoms: 'Sentòm yo',
        when_to_see_doctor: 'Lè pou w wè yon doktè',
         causes: 'Kòz',
         risk_factors: 'Faktè risk',
        complications: 'Konplikasyon',
         prevention: 'Prevansyon',
         diagnosis: 'Dyagnostik',
         treatment: 'Tretman',
          self_care: 'Oto-swen',
          preparing_for_your_appointment: 'Prepare pou randevou ou',
      },
       mi: {
          overview: 'Tirohanga whānui',
        symptoms: 'Ngā tohu',
        when_to_see_doctor: 'Āhea te kite i te tākuta',
          causes: 'Ngā take',
         risk_factors: 'Ngā āhuatanga tūpono',
        complications: 'Ngā raruraru',
          prevention: 'Te ārai',
        diagnosis: 'Te tātaritanga',
         treatment: 'Te rongoā',
          self_care: 'Te tiaki whaiaro',
          preparing_for_your_appointment: 'Te whakarite mō tō whakaritenga',
    },
     haw: {
      overview: 'Nānaina',
       symptoms: 'Nā hōʻailona',
        when_to_see_doctor: 'ʻO ka manawa hea e ʻike ai i ke kauka',
        causes: 'Nā kumu',
         risk_factors: 'Nā mea pilikia',
         complications: 'Nā pilikia',
        prevention: 'Ka pale ʻana',
          diagnosis: 'ʻImi ʻike',
          treatment: 'Ka mālama ʻana',
         self_care: 'Ka mālama ponoʻī',
         preparing_for_your_appointment: 'Ka hoʻomākaukau ʻana no kāu koho',
    },
    la: {
        overview: 'Conspectus',
         symptoms: 'Signa',
        when_to_see_doctor: 'Quando medicum videre',
         causes: 'Causae',
         risk_factors: 'Res Periculi',
        complications: 'Complicationes',
         prevention: 'Praeventio',
         diagnosis: 'Diagnosis',
          treatment: 'Curatio',
          self_care: 'Cura Sui',
         preparing_for_your_appointment: 'Praeparans pro tua destinatione',
      }
  };

  export default sectionMappings; 