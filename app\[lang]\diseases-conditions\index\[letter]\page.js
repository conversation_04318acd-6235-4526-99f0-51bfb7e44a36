import DiseasesByLetterClient from './DiseasesByLetterClient';
import { fetchByLetter } from '../../../../lib/api';
import { getConditionIndexMetaData } from '@/app/api/diseases-conditions/meta/getMetaDataIndex';
import translationStrings from '../../language/translations';
const logger = require('../../../../utils/logger');

const revalidate = 3600; // Revalidate every hour

export default async function DiseasesByLetterPage({ params }) {
  const { letter = '', lang: language = 'en' } = await params;
  const headers = {
    'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400'
  };
  
  // Create a safe version of langStrings without functions
  const langStrings = {
    ...translationStrings[language] || translationStrings.en,
    // Pre-compute the noConditionsFound string
    noConditionsFoundMessage: translationStrings[language]?.noConditionsFound(letter) || ''
  };

  // Remove the function to avoid serialization issues
  delete langStrings.noConditionsFound;

  try {
    const data = await fetchByLetter('diseases-conditions', letter, language, 1);
    const metaIndexTitle = data?.items?.[0]?.page_indextitle || langStrings.title;
    const metaDescription = data?.items?.[0]?.page_description || langStrings.title;
    
    const conditions = (data.items || []).map(item => ({
      id: item.id,
      name: item.name,
      slug: item.slug,
    }));

    const initialData = {
      items: conditions,
      pagination: data.pagination
    };

    return (
      <DiseasesByLetterClient 
        initialData={initialData}
        letter={letter}
        language={language}
        metaIndexTitle={metaIndexTitle}
        metaDescription={metaDescription}
        langStrings={langStrings}
      />
    );
  } catch (error) {
    logger.error('Error in page:', error);
    return (
      <DiseasesByLetterClient 
        initialData={{ items: [], pagination: { total: 0, page: 1, limit: 20, totalPages: 0 } }}
        letter={letter}
        language={language}
        metaIndexTitle={langStrings.title}
        metaDescription={langStrings.title}
        langStrings={langStrings}
        error={error.message}
      />
    );
  }
}
