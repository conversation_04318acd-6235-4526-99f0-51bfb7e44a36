// Import createMDX 
import pkg from '@next/mdx';
const createMDX = pkg.default || pkg;

// Create the withMDX function
const withMDX = createMDX({
  extension: /\.mdx?$/,
});

// Bundle analyzer
const withBundleAnalyzer = (await import('@next/bundle-analyzer')).default({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["react-helmet-async"],
  pageExtensions: ["js", "jsx", "ts", "tsx", "md", "mdx"],

  // 👇 Serve assets from custom domain using env variable
  assetPrefix: process.env.NEXT_PUBLIC_API_URL || '',

  basePath: '',

  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;

      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          mui: {
            name: 'mui',
            test: /[\/\\]node_modules[\/\\]@mui[\/\\]/,
            chunks: 'all',
            priority: 30,
            enforce: true,
          },
          react: {
            name: 'react',
            test: /[\/\\]node_modules[\/\\](react|react-dom)[\/\\]/,
            chunks: 'all',
            priority: 20,
            enforce: true,
          },
          lodash: {
            name: 'lodash',
            test: /[\/\\]node_modules[\/\\]lodash[\/\\]/,
            chunks: 'all',
            priority: 25,
            enforce: true,
          },
          vendor: {
            name: 'vendor',
            test: /[\/\\]node_modules[\/\\]/,
            chunks: 'all',
            priority: 10,
            minSize: 20000,
            maxSize: 200000,
          },
        },
      };

      config.module.rules.push({
        test: /[\/\\]node_modules[\/\\](@mui|lodash)[\/\\]/,
        sideEffects: false,
      });
    }
    return config;
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "framerusercontent.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.usmle.org",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.pexels.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.pexels.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "augustbuckets.blob.core.windows.net",
        pathname: "/**",
      },
      // 👇 Use your env domain instead of hardcoding
      {
        protocol: "https",
        hostname: process.env.NEXT_PUBLIC_API_DOMAIN || 'library.meetaugust.ai',
        pathname: "/**",
      },
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  experimental: {
    optimizeCss: false,
    scrollRestoration: true,
  },

  compress: true,
};

export default withBundleAnalyzer(withMDX(nextConfig));
