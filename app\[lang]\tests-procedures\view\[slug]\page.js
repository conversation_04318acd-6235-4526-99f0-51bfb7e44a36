import translationStrings from '../../language/translations';
import TestProcedureClient from './TestProcedureClient';
const logger = require('../../../../utils/logger');

async function fetchTest(slug, lang) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/tests-procedures/view/${slug}?lang=${lang}`, {
      method: 'GET',
      headers: {
        'Accept-Language': lang,
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error('Failed to fetch test');
    }
    return response.json();
  } catch (error) {
    logger.error('Error fetching test:', error);
    throw error;
  }
}

export default async function TestProcedurePage({ params }) {
  const { slug, lang } = params;
  const language = lang || 'en';
  
  // Create a safe version of langStrings without functions
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    // Remove any functions to avoid serialization issues
    noTestsFound: undefined
  };

  try {
    // Fetch test data server-side
    const test = await fetchTest(slug, language);
    const { name, meta_title, meta_description } = test;
    const metaTitle = meta_title || name || 'Default Title';
    const metaDescription = meta_description || name || 'Default Description';

    return (
      <TestProcedureClient
        test={test}
        language={language}
        langStrings={langStrings}
        metaTitle={metaTitle}
        metaDescription={metaDescription}
      />
    );
  } catch (error) {
    logger.error('Error in TestProcedurePage:', error);
    return (
      <TestProcedureClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        metaTitle={langStrings.title || ''}
        metaDescription={langStrings.description || ''}
      />
    );
  }
}