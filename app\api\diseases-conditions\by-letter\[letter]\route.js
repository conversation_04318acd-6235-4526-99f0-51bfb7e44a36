import { query } from '@/app/lib/db';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

export async function GET(request, { params }) {
  try {
    const { letter } = await params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const offset = (page - 1) * limit;

    const languageId = languageMap[lang] || 1;
    
    let result;

    if (languageId != 1) {
      result = await query(
        `WITH data AS (
          SELECT
            c.id,
            COALESCE(ct.name, c.name) AS name,
            c.slug,
            pct.title AS page_title,
            pct.description AS page_description,
            pct.indextitle AS page_indextitle,
            COUNT(*) OVER() AS total_count
          FROM
            condition c
          LEFT JOIN condition_translations ct ON c.id = ct.condition_id AND ct.language_id = $2
          LEFT JOIN conditions_page_meta_tags pct ON ct.language_id = pct.language_id
          WHERE
            ct.first_letter = UPPER($1)
          ORDER BY
            COALESCE(ct.name, c.name) ASC
          LIMIT $3 OFFSET $4
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, languageId, limit, offset]
      );
    } else {
      result = await query(
        `WITH data AS (
          SELECT
            c.id,
            c.name,
            c.slug,
            pct.title AS page_title,
            pct.description AS page_description,
            pct.indextitle AS page_indextitle,
            COUNT(*) OVER() AS total_count
          FROM
            condition c
          LEFT JOIN conditions_page_meta_tags pct ON pct.language_id = 1
          WHERE
            c.first_letter = UPPER($1)
          ORDER BY
            c.name ASC
          LIMIT $2 OFFSET $3
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, limit, offset]
      );
    }

    const totalCount = result.rows[0]?.total || 0;

    return Response.json({
      items: result.rows,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load diseases and conditions' },
      { status: 500 }
    );
  }
}