<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Calculateur de Besoins Caloriques Quotidiens - Calculateur BMR & TDEE Précis | MeetAugust</title>
    <meta name="description" content="Calculez vos besoins caloriques quotidiens avec notre calculateur BMR avancé. Obtenez des objectifs caloriques personnalisés pour la perte de poids, le maintien et la prise de muscle. Gratuit, précis et basé sur la science." />
    <meta name="keywords" content="calculateur de calories, calculateur BMR, calculateur TDEE, besoins caloriques quotidiens, calculateur perte de poids, calculateur métabolisme, planification nutritionnelle" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="Calculateur de Besoins Caloriques Quotidiens - Calculateur BMR & TDEE Précis" />
    <meta property="og:description" content="Calculez vos besoins caloriques quotidiens avec notre calculateur BMR avancé. Obtenez des objectifs caloriques personnalisés pour la perte de poids, le maintien et la prise de muscle." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Calculateur de Besoins Caloriques Quotidiens - Calculateur BMR & TDEE Précis" />
    <meta name="twitter:description" content="Calculez vos besoins caloriques quotidiens avec notre calculateur BMR avancé. Obtenez des objectifs caloriques personnalisés pour la perte de poids, le maintien et la prise de muscle." />
    <link rel="canonical" href="https://www.meetaugust.ai/calculator/calorie" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/fr/calculators/calorie-calculator" />
    <link rel="icon" href="/fr/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/fr/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img
            width="200"
            src="/fr/calculators/assets/august_logo_green_nd4fn9.svg"
            alt="Logo du Calculateur"
          />
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select" style="border:1px solid #e5e7eb;border-radius:6px;padding:6px 8px;font-size:14px;color:#374151;background:#fff;outline:none;">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/redirect/wa?message=Hello%20August"
            class="talk-to-august"
            >Parler à August</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>Calculateur Avancé des Besoins Caloriques Quotidiens</h1>
        <p>Découvrez combien de calories vous avez besoin chaque jour avec notre calculateur précis. Parfait pour la perte de poids, la prise de muscle, <br /> ou le maintien d'un mode de vie sain selon votre corps, vos objectifs et votre niveau d'activité.</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">Unités US</button>
            <button class="tab-button active" data-unit="metric">Unités Métriques</button>
          </div>
          <p class="form-instruction">Entrez vos informations personnelles ci-dessous et cliquez sur Calculer pour obtenir vos recommandations caloriques personnalisées</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Âge</label>
              <input type="text" id="age" value="25" />
              <span>âges 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Genre</label>
              <input type="radio" name="gender" value="male" checked /> Homme
              <input type="radio" name="gender" value="female" /> Femme
            </div>
            <div class="form-field">
              <label for="height">Taille</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>pieds</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>pouces</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">Poids</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>livres</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">Activité</label>
              <select id="activity">
                <option value="sedentary">Sédentaire : peu ou pas d'exercice</option>
                <option value="light">Légèrement actif : exercice léger 1-3 jours/semaine</option>
                <option value="moderate" selected>Modérément actif : exercice modéré 3-5 jours/semaine</option>
                <option value="very">Très actif : exercice intense 6-7 jours/semaine</option>
                <option value="super">Super actif : exercice très intense, travail physique</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ Paramètres</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>Paramètres</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>Unité des résultats :</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">Calories</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">Kilojoules</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Pourcentage de Masse Grasse :
                  <span
                    class="info-icon"
                    title="Entrez votre pourcentage de masse grasse pour des calculs de composition corporelle plus précis"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Formule d'estimation BMR :
                  <span
                    class="info-icon"
                    title="Choisissez la formule pour calculer votre Métabolisme de Base"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div |cutoff|>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">Harris-Benedict Révisée</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Calculer ▶
              </button>
              <button type="button" class="clear-button">Effacer</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>Résultat</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="Télécharger les résultats en PDF"
            >
              <span class="download-icon">📥</span>
              <span>Télécharger PDF</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">Vos objectifs caloriques personnalisés sont calculés à l'aide de formules métaboliques avancées. Ces recommandations fournissent des lignes directrices d'apport calorique quotidien adaptées à vos objectifs spécifiques - que vous souhaitiez maintenir votre poids actuel, atteindre une perte de poids durable ou soutenir une prise de poids saine.</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                Métabolisme de Base (BMR) :
                <span id="bmr-value" style="color: #111827">1,650</span>
                calories/jour
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  Niveaux d'Activité :
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sédentaire:</strong> peu ou pas d'exercice
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Léger:</strong> exercice 1-3 fois/semaine
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Modéré:</strong> exercice 4-5 fois/semaine
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>Actif:</strong> exercice quotidien ou intense 3-4 fois/semaine
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Très Actif:</strong> exercice intense 6-7 fois/semaine
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Extra Actif:</strong> exercice très intense quotidiennement, ou travail physique
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">Objectif</th>
                  <th style="width: 30%">Calories</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="Objectif">
                    <div class="goal-label">Maintenir le poids</div>
                  </td>
                  <td data-label="Calories Journalières">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">calories/jour</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Objectif">
                    <div class="goal-label">Perte de poids légère</div>
                    <div class="goal-description">0,5 lb/semaine</div>
                  </td>
                  <td data-label="Calories Journalières">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">calories/jour</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="Objectif">
                    <div class="goal-label">Perte de poids</div>
                    <div class="goal-description">1 lb/semaine</div>
                  </td>
                  <td data-label="Calories Journalières">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">calories/jour</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Objectif">
                    <div class="goal-label">Perte de poids extrême</div>
                    <div class="goal-description">2 lb/semaine</div>
                  </td>
                  <td data-label="Calories Journalières">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">calories/jour</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >Afficher les infos pour la prise de poids</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                Informations sur la Prise de Poids
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">Objectif</th>
                    <th style="width: 30%">Calories</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Objectif">
                      <div class="goal-label">Prise de poids légère</div>
                      <div class="goal-description">0,25 kg/semaine</div>
                    </td>
                    <td data-label="Calories Journalières">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">calories/jour</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="Objectif">
                      <div class="goal-label">Prise de poids</div>
                      <div class="goal-description">0,5 kg/semaine</div>
                    </td>
                    <td data-label="Calories Journalières">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">calories/jour</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Objectif">
                      <div class="goal-label">Prise de poids rapide</div>
                      <div class="goal-description">1 kg/semaine</div>
                    </td>
                    <td data-label="Calories Journalières">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">calories/jour</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>Recommandations d'Activité Physique</h2>
          <ul>
            <li>
              <strong>Exercice Léger:</strong> 20-40 minutes d'activités d'intensité modérée comme la marche ou le yoga doux.
            </li>
            <li>
              <strong>Exercice Modéré:</strong> 30-60 minutes d'activités qui augmentent votre fréquence cardiaque, comme la marche rapide ou le vélo.
            </li>
            <li>
              <strong>Exercice Vigoureux:</strong> 45-90 minutes d'entraînement intensif, de sports ou d'activités physiques exigeantes.
            </li>
            <li>
              <strong>Entraînement Professionnel/Athlétique:</strong> 2+ heures d'entraînement intensif ou de travail professionnel physiquement exigeant.
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>Guide de Référence Nutritionnelle Complet</h2>
          <p>Utilisez ces tableaux complets pour prendre des décisions alimentaires éclairées et mieux comprendre la teneur en calories des aliments quotidiens, les stratégies de planification des repas et la dépense énergétique de l'exercice.</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Teneur Calorique des Aliments Populaires
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Aliment
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Taille de Portion
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Calories
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Fruits Frais
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mangue
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 moyenne (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kiwi
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 grand (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Myrtilles
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Avocat
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 moyen (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Cerises
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Légumes Frais
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Patate douce
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 moyenne (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Poivron
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse tranchée
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Épinards
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2 tasses fraîches
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Courgette
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse tranchée
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Chou-fleur
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Sources de Protéines
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Saumon, grillé
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Blanc de dinde
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Yaourt grec
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pot de 6 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Amandes
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 oz. (23 amandes)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lentilles, cuites
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Céréales & Féculents
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Quinoa, cuit
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Flocons d'avoine
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse cuite
                    </td>
                    <td style="padding: 8/*cutoff*/px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pâtes complètes
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse cuite
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Riz complet
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse cuite
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Boissons
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Thé vert
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lait d'amande
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Eau de coco
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Vin rouge
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              Note : Les valeurs caloriques sont approximatives et peuvent varier selon les méthodes de préparation et les marques spécifiques.
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Planification Stratégique des Repas
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Période du Repas
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plan de 1 300 Calories
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plan de 1 600 Calories
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plan de 2 100 Calories
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Petit-déjeuner
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 tasse de yaourt grec avec 1/2 tasse de myrtilles (130 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tasse de flocons d'avoine avec 1/2 tasse de myrtilles (238 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tasse de flocons d'avoine avec 1 kiwi, 1 oz. d'amandes (458 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Collation Matinale
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 petit kiwi (56 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mangue moyenne (135 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mangue moyenne, 10 cerises (232 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Matinée
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      186 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      373 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      690 cal
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Déjeuner
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      2 tasses de salade d'épinards avec 3 oz. de saumon grillé (189 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de blanc de dinde, 1 tasse de courgette, 1/2 tasse de quinoa (264 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de saumon grillé, 1 tasse de riz complet, 1 tasse de chou-fleur (418 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Collation de l'Après-midi
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tasse de poivron tranché (28 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 avocat (160 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 avocat, 1 oz. d'amandes (324 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Midi
                    </td>
                    <td
.PerformAction("cutoff") style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      217 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      424 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      742 cal
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Dîner
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de blanc de dinde, 1 tasse de chou-fleur (150 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de saumon grillé, 1 tasse de patate douce (287 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de blanc de dinde, 1 tasse de pâtes complètes, 1 tasse d'épinards (313 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Collation du Soir
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tasse de thé vert (2 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tasse d'eau de coco (46 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tasse de yaourt grec (130 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Soirée
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      152 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      333 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      443 cal
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Total Journalier
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1 255 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1 630 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2 175 cal
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>Maîtrisez Votre Métabolisme</h2>
          <p>Comprendre votre métabolisme est essentiel pour atteindre vos objectifs de santé. Ce calculateur utilise des formules scientifiquement validées pour estimer votre Métabolisme de Base (BMR) et votre Dépense Énergétique Totale Quotidienne (TDEE).</p>

          <div class="equations-container">
            <h3>Trois Formules Prouvées pour le BMR</h3>
            <p>Ce calculateur utilise trois équations bien étudiées pour estimer votre BMR, chacune ayant des atouts uniques selon votre profil :</p>

            <div class="equation-card">
              <h4>Équation de Mifflin-St Jeor</h4>
              <p>
                <strong>Homme:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>Femme:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                Considérée comme la plus précise pour la population générale, surtout pour les non-athlètes.
              </p>
            </div>

            <div class="equation-card">
              <h4>Équation Harris-Benedict Révisée</h4>
              <p>
                <strong>Homme:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>Femme:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                Une formule fiable mise à jour pour une précision moderne, adaptée à un large éventail d'individus.
              </p>
            </div>

            <div class="equation-card">
              <h4>Formule Katch-McArdle</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                Idéale pour les personnes connaissant leur pourcentage de masse grasse, car elle prend en compte la masse maigre.
              </p>
            </div>

            <div class="info-text">
              <h3>TDEE : Transformer le BMR en Objectifs Concrets</h3>
              <p>Votre Dépense Énergétique Totale Quotidienne (TDEE) est votre BMR multiplié par un facteur d'activité reflétant votre mode de vie. Cela vous donne une image complète de vos besoins caloriques quotidiens.</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Niveau d'Activité
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Description
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Multiplicateur
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sédentaire
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        peu ou pas d'exercice
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Légèrement Actif
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercice 1-3 fois/semaine
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Modérément Actif
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercice 4-5 fois/semaine
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Très Actif
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercice intense 6-7 fois/semaine
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Super Actif
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercice très intense quotidiennement, ou travail physique
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>Le résultat est une estimation précise des calories que vous brûlez chaque jour, que vous pouvez utiliser pour adapter votre alimentation à la perte de poids, au maintien ou à la prise de poids.</p>

              <h3>Gestion Stratégique du Poids</h3>
              <p>Pour perdre du poids, il faut un déficit calorique ; pour en prendre, un surplus. Ce calculateur fournit des objectifs caloriques précis pour chaque but, assurant un progrès durable.</p>

              <div class="warning-note">
                <strong>Lignes Directrices Critiques pour une Perte de Poids Sûre :</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Évitez les déficits extrêmes pour ne pas ralentir votre métabolisme.</li>
                  <li>Assurez un apport protéique adéquat pour préserver la masse musculaire.</li>
                  <li>Privilégiez les aliments riches en nutriments pour éviter les carences.</li>
                  <li>Évitez les régimes drastiques pour prévenir la reprise de poids.</li>
                </ul>
                <p style="margin-top: 12px">Pour des résultats optimaux, consultez un nutritionniste ou un diététicien pour personnaliser davantage votre plan.</p>
              </div>

              <h3>Stratégies d'Optimisation Nutritionnelle</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>Privilégiez les aliments riches en nutriments comme les légumes, les protéines maigres et les céréales complètes.</li>
                <li>Assurez un apport protéique adéquat pour soutenir la masse musculaire et la satiété.</li>
                <li>Rejetez les régimes trop restrictifs au profit d'une alimentation équilibrée et durable.</li>
                <li>Suivez votre apport de façon cohérente pour créer des habitudes saines à long terme.</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                Ce calculateur est un point de départ. Ajustez selon vos progrès et consultez des professionnels pour des conseils personnalisés.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>Suivi Précis des Calories</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>Étape 1 : Déterminez Votre Base Métabolique</h4>
              <p>Utilisez ce calculateur pour trouver votre BMR et TDEE selon votre âge, genre, poids, taille et niveau d'activité.</p>
            </div>

            <div class="step-card">
              <h4>Étape 2 : Établissez Vos Objectifs de Poids</h4>
              <p>Fixez des objectifs réalistes pour la perte de poids, le maintien ou la prise, en utilisant les recommandations caloriques fournies.</p>
            </div>

            <div class="step-card">
              <h4>Étape 3 : Mettez en Place des Systèmes de Suivi</h4>
              <p>Suivez votre apport calorique avec des applications ou un journal alimentaire pour rester aligné sur vos objectifs.</p>
              <p>Pesez-vous chaque semaine et surveillez les tendances, pas les fluctuations quotidiennes.</p>
            </div>

            <div class="step-card">
              <h4>Étape 4 : Optimisez par l'Évaluation</h4>
              <p>Réévaluez vos besoins caloriques toutes les 4-6 semaines ou après des changements de poids importants pour garder votre plan efficace.</p>
            </div>
          </div>

          <div class="info-text">
            <h3>La Science de l'Équilibre Calorique</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>Fondamentaux de l'Équilibre Énergétique:</strong> La gestion du poids est régie par les calories consommées contre les calories dépensées.</li>
              <li><strong>Effet Thermique des Aliments:</strong> Différents aliments nécessitent des quantités d'énergie variables pour être digérés, impactant le total de calories brûlées.</li>
              <li><strong>Satiété et Qualité des Aliments:</strong> Les aliments riches en fibres et en protéines favorisent la satiété, vous aidant à suivre votre plan.</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              Exemple : Le 'Régime Twinkie' a montré que la perte de poids est possible avec un déficit calorique, mais les régimes pauvres en nutriments nuisent à la santé à long terme.
            </p>

            <h4 style="color: #416955; margin-top: 20px">Bénéfices Supplémentaires du Suivi des Calories</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Développe la conscience nutritionnelle et des habitudes alimentaires réfléchies.</li>
              <li>Améliore le contrôle des portions grâce à un suivi cohérent.</li>
              <li>Relie les choix alimentaires à l'exercice pour optimiser l'équilibre énergétique.</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Cyclage Calorique Zigzag</h2>
          <p>Le cyclage calorique zigzag consiste à varier votre apport calorique quotidien tout en maintenant votre objectif hebdomadaire pour améliorer la flexibilité métabolique et éviter les plateaux.</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>Exemple</h4>
              <p><strong>Objectif Hebdomadaire:</strong> 14 000 calories (2 000 calories/jour en moyenne)</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>Option A:</strong> 7 jours à 2 000 calories.</li>
                <li><strong>Option B:</strong> 5 jours à 1 800 calories, 2 jours à 2 500 calories.</li>
              </ul>
              <p>Les deux options atteignent l'objectif hebdomadaire mais varient l'apport quotidien pour garder votre métabolisme dynamique.</p>
            </div>

            <div class="benefits-card">
              <h4>Objectif Zigzag</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>Meilleure flexibilité métabolique et adhésion.</li>
                <li>Plus de flexibilité dans la planification des repas, surtout pour les événements sociaux.</li>
                <li>Prévient l'adaptation métabolique due à des déficits prolongés.</li>
                <li>Brise les plateaux de perte de poids en variant l'apport calorique.</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>Besoins Caloriques par Mode de Vie</h2>
          <p>Les besoins caloriques varient selon les facteurs individuels, mais des lignes directrices générales peuvent vous aider à commencer.</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Facteurs qui Influencent les Besoins Caloriques</h4>
              <ul>
                <li>Âge, sexe, poids et taille.</li>
                <li>Niveau d'activité (sédentaire à très actif).</li>
                <li>État de santé, y compris grossesse ou conditions médicales.</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>Lignes Directrices Générales</h4>
              <p><strong>Hommes:</strong> 2 000–3 000 calories/jour</p>
              <p><strong>Femmes:</strong> 1 600–2 400 calories/jour</p>
            </div>

            <div class="minimum-card">
              <h4>Apport Minimum Sûr</h4>
              <p><strong>Femmes:</strong> 1 200 calories/jour</p>
              <p><strong>Hommes:</strong> 1 500 calories/jour</p>
              <p class="warning">Les apports inférieurs à ces niveaux doivent être supervisés médicalement.</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>Restreindre excessivement les calories peut entraîner des carences nutritionnelles, une perte musculaire et un ralentissement du métabolisme. Priorisez toujours la santé.</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>Toutes les Calories ne se Valent pas</h2>
          <p>Les calories proviennent de différents macronutriments, chacun ayant des effets uniques sur votre corps :</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>Protéines : 4 calories/gramme – soutient la réparation musculaire et la satiété.</li>
            <li>Glucides : 4 calories/gramme – principale source d'énergie.</li>
            <li>Lipides : 9 calories/gramme – essentiels pour les hormones et l'absorption des nutriments.</li>
            <li>Alcool : 7 calories/gramme – valeur nutritionnelle minimale.</li>
          </ul>

          <p>Les étiquettes nutritionnelles fournissent des comptes caloriques précis, mais les tailles de portion et les méthodes de préparation comptent.</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>Aliments Riches en Calories</h4>
              <p>Denses en calories, souvent à cause des graisses ou des sucres. À utiliser avec modération pour la gestion du poids.</p>
              <ul>
                <li>Avocats, huiles.</li>
                <li>Noix et graines.</li>
                <li>Aliments frits.</li>
                <li>Desserts et snacks sucrés.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Aliments à Faible Teneur en Calories</h4>
              <ul>
                <li>De nombreux légumes (ex. épinards, courgettes).</li>
                <li>Certains fruits (ex. baies).</li>
                <li>Protéines maigres (ex. dinde, poisson).</li>
                <li>Céréales complètes avec modération.</li>
                <li>Légumes à feuilles pour le volume et les nutriments.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Calories Vides</h4>
              <ul>
                <li>Boissons sucrées (ex. soda).</li>
                <li>Snacks transformés (ex. chips, biscuits).</li>
                <li>Sucres ajoutés dans les aliments emballés.</li>
                <li>Graisses solides (ex. beurre, margarine).</li>
                <li>Alcool avec bénéfice nutritionnel minimal.</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>Pourquoi la Qualité Calorique Compte</h3>
            <p>Les boissons comme le soda ou l'alcool ajoutent des calories sans satiété, rendant plus difficile le maintien d'un déficit.</p>

            <h4 style="color: #416955; margin-top: 16px">Élaborez un Plan Équilibré</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Concentrez-vous sur des aliments entiers et non transformés pour la densité nutritionnelle.</li>
              <li>Limitez les snacks et boissons sucrés.</li>
              <li>Utilisez des aliments riches en fibres et en protéines pour un contrôle naturel des portions.</li>
              <li>Combinez le comptage des calories avec l'exercice pour des résultats durables.</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>Conclusion Finale</h2>
          <p>Il n'y a pas d'approche unique pour la nutrition. Utilisez ce calculateur comme point de départ, suivez vos progrès et ajustez selon vos besoins uniques.</p>
          <p style="font-weight: 500; color: #416955">Suivez intelligemment, mangez consciemment et priorisez la santé à long terme.</p>
        </section>
      </main>
    </div>
    <script src="/fr/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>