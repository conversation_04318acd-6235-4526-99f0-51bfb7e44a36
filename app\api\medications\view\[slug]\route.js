import { query } from '@/app/lib/db';
import sectionMappings from '../../sectionMappings';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');
// Fields that should be rendered as markdown
const markdownFields = ['brand_names', 'description', 'before_using', 'proper_use', 'sideeffects'];
const languageIdCache = {};
async function getLanguageId(lang) {
  if (languageIdCache[lang]) {
    logger.info(`Language ID cache hit for ${lang}: ${languageIdCache[lang]}`); // Optional logging
    return languageIdCache[lang];
  }
  const languageIdQuery = `SELECT id FROM languages WHERE code = $1`;
  try {
    const languageIdResult = await query(languageIdQuery, [lang]);
    const languageId = languageIdResult.rows[0]?.id || null; // Handle cases where the language is not found
    languageIdCache[lang] = languageId;
    logger.info(`Language ID cache miss for ${lang}, fetched: ${languageId}`); // Optional logging
    return languageId;
  } catch (error) {
    logger.error(`Error fetching language ID for ${lang}:`, error);
    return null; // Return null or a default value in case of an error
  }
}
export async function GET(request, { params }) {
  try {
    const { slug } = params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';
    const languageId = languageMap[lang] || 1;
    let result;
    if (languageId != 1) {
      result = await query(
        `SELECT
    m.id,
    COALESCE(mt.name, m.name) AS name,
    m.slug,
    m.brand_names AS m_brand_names,
    m.description AS m_description,
    m.before_using AS m_before_using,
    m.proper_use AS m_proper_use,
    m.side_effects AS m_side_effects,
    mt.brand_names AS brand_names,
    mt.description AS description,
    mt.body_html AS body_html,
    mt.before_using AS before_using,
    mt.proper_use AS proper_use,
    mt.side_effects AS side_effects,
    mmt.title AS meta_title,
    mmt.description AS meta_description,
    m.created_at
FROM
    medications_new m
LEFT JOIN
    medications_translations_new mt ON m.id = mt.medications_id AND mt.language_id = $2
LEFT JOIN
    medications_meta_tags mmt ON m.id = mmt.medication_id AND mmt.language_id = $2
WHERE
    m.slug = $1;
        `,
        [slug, languageId]
      );
    } else {
      result = await query(
        `SELECT
    m.id,
    m.name,
    m.slug,
    m.brand_names,
    m.description,
    m.before_using,
    m.proper_use,
    m.body_html,
    m.side_effects,
    mmt.title AS meta_title,
    mmt.description AS meta_description,
    m.created_at
FROM
    medications_new m
LEFT JOIN
    medications_meta_tags mmt ON m.id = mmt.medication_id AND mmt.language_code = 'en'
WHERE
    m.slug = $1;
        `,
        [slug]
      );
    }
    if (result.rows.length === 0) {
      return Response.json(
        { error: 'Medication not found' },
        { status: 404 }
      );
    }
    const medication = result.rows[0];
    // Transform the data to include sections for markdown content
    const sections = markdownFields
      .filter(field => {
        const translatedContent = medication[field];
        const englishContent = medication[`m_${field}`];
        return (translatedContent || englishContent) &&  (translatedContent !== null && translatedContent !== undefined ) || (englishContent !== null && englishContent !== undefined)
      })
      .map(field => ({
        heading: sectionMappings[lang][field],
        content: medication[field] || medication[`m_${field}`]
      }));
    // Format brand names if they exist
    const brandNames = medication.brand_names
      ? medication.brand_names.split(',').map(name => name.trim())
      : [];
    // Construct the response
    const response = {
      id: medication.id,
      name: medication.name,
      slug: medication.slug,
      brand_names: brandNames,
      sections: sections,
      body_html: medication.body_html,
      meta: {
        created_at: medication.created_at,
      },
      meta_title: medication.meta_title,
      meta_description: medication.meta_description,
    };
    logger.info('medication', response);
    return Response.json(response);
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load medication details' },
      { status: 500 }
    );
  }
}
