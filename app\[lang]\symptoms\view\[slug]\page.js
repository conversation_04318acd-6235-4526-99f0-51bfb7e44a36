import { getSymptomMetadata } from '../../../../api/symptoms/meta/getMetaData';
import translationStrings from '../../language/translations';
import SymptomViewClient from './SymptomViewClient';
const logger = require('../../../../utils/logger');

async function fetchSymptom(slug, lang) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/symptoms/view/${slug}?lang=${lang}`, {
      method: 'GET',
      headers: {
        'Accept-Language': lang,
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error('Failed to fetch symptom');
    }
    return response.json();
  } catch (error) {
    logger.error('Error fetching symptom:', error);
    throw error;
  }
}

export default async function SymptomPage({ params }) {
  const { slug, lang } = params;
  const language = lang || 'en';
  
  // Create a safe version of langStrings without functions
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    // Remove any functions to avoid serialization issues
    noSymptomsFound: undefined,
    // Add any other functions that need to be removed
  };

  try {
    // Fetch symptom data server-side
    const symptom = await fetchSymptom(slug, language);
    const { meta_title, meta_description } = symptom;
    const metaTitle = meta_title || symptom.name || 'Default Title';
    const metaDescription = meta_description || symptom.name || 'Default Description';

    return (
      <SymptomViewClient
        symptom={symptom}
        language={language}
        langStrings={langStrings}
        metaTitle={metaTitle}
        metaDescription={metaDescription}
      />
    );
  } catch (error) {
    logger.error('Error in SymptomPage:', error);
    return (
      <SymptomViewClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        metaTitle={langStrings.title || ''}
        metaDescription={langStrings.description || ''}
      />
    );
  }
}