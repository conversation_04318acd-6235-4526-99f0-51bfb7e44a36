curl --location --request POST 'http://127.0.0.1:8040/c/baby-md/register' \
--header 'Content-Type: application/json' \
--data-raw '{
    "$id": "1",
    "CreatedByType": 1,
    "ModifiedByType": 0,
    "Title": "",
    "FirstName": "BabyGirl",
    "MiddleName": "",
    "LastName": "August",
    "Category": "",
    "Notes": null,
    "LeadID": "d2a081af-30c8-4968-9b91-c8c7a24599ee",
    "DOBDateString": "2025-01-10",
    "DOBSignedOffsetInMins": 330.0,
    "DOB": "2025-01-10T00:00:00",
    "Gender": "M",
    "PatientImageOnlineURL": "",
    "PatientImageThumbnailOnlineURL": "",
    "PatientImageID": null,
    "PatientImageOnlineName": null,
    "PatientImageThumbnailOnlineName": null,
    "Email": "<EMAIL>",
    "PreviousEmail": "<EMAIL>",
    "Mobile": "**********",
    "PreviousMobile": "+910123456789",
    "PatientUniqueIDType": "",
    "PatientUniqueIDNumber": "",
    "MobileCountryCode": "+91",
    "FamilyID": "22",
    "FullName": "BabyBoy August",
    "IsFamilyHead": true,
    "Age": "20d",
    "AgeInYear": 0,
    "BelongToOrgID": "ff0e7efa-7aac-4872-a42e-8ad2b43569c9",
    "BelongToOrgHeadID": "ff0e7efa-7aac-4872-a42e-8ad2b43569c9",
    "BelongToOrgName": "BabyMD Sandbox",
    "Address1": "",
    "Address2": "",
    "Address3": "",
    "City": "",
    "State": "",
    "PostalCode": "",
    "Country": "",
    "Nationality": "",
    "Occupation": "",
    "MaritalStatus": "",
    "Language": "",
    "Religion": "",
    "NabidhShareDataPolicy": "",
    "CompanyName": null,
    "IsCorporateAdmin": false,
    "BelongsToCorporateID": "",
    "BelongsToCorporateName": "",
    "CommunicationPreference": 1,
    "CommunicationPreferenceString": "SMS",
    "Source": "",
    "SubSource": null,
    "Channel": null,
    "SourceDesc": "",
    "RegistrationDateString": "2025-01-30",
    "RegistrationDateSignedOffsetInMins": 330.0,
    "RegistrationDate": "2025-01-30T00:00:00",
    "AmniversaryDateString": "",
    "AmniversaryDateSignedOffsetInMins": 0.0,
    "AmniversaryDate": "0001-01-01T00:00:00",
    "FileNo": "",
    "Phone1": "",
    "Phone1CountryCode": "",
    "Phone2": "",
    "Phone2CountryCode": "",
    "Fax": "",
    "FaxCountryCode": "",
    "SkypeID": "",
    "Website": "",
    "ContactEmail1": "<EMAIL>",
    "ContactEmail2": "",
    "EmergencyContactPersonName": "",
    "EmergencyContactPersonRelationWithPatient": "",
    "EmergencyContactPersonContactNumber": "",
    "EmergencyContactPersonContactNumberCode": "+91",
    "BloodGroup": "",
    "RegistrationName1": "Reg. No:Name1",
    "RegistrationName2": "",
    "RegistrationNumber1": "Reg. No:Name1",
    "RegistrationNumber2": "",
    "RegistrationName3": "Reg. No:Name3",
    "RegistrationNumber3": "",
    "PointsEarned": 0,
    "PatientCustomFields": {
        "$id": "2",
        "$values": [
            {
                "$id": "3",
                "Name": "Custom",
                "Value": "Parent1 Name",
                "Param1": "Dad August",
                "Param2": "Text",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            },
            {
                "$id": "4",
                "Name": "Custom",
                "Value": "Lead Sub Source",
                "Param1": "",
                "Param2": "DropDown",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            },
            {
                "$id": "5",
                "Name": "Custom",
                "Value": "Channel",
                "Param1": "",
                "Param2": "DropDown",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            },
            {
                "$id": "6",
                "Name": "Custom",
                "Value": "Baby Service Name 2",
                "Param1": "",
                "Param2": "DropDown",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            },
            {
                "$id": "7",
                "Name": "Custom",
                "Value": "Lead Created Time",
                "Param1": "",
                "Param2": "DateTime",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            },
            {
                "$id": "8",
                "Name": "Custom",
                "Value": "Followup Date Time",
                "Param1": "",
                "Param2": "DateTime",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            },
            {
                "$id": "9",
                "Name": "Custom",
                "Value": "Appointment Date Time",
                "Param1": "",
                "Param2": "DateTime",
                "Param3": null,
                "Param4": null,
                "Param5": null,
                "Param6": null,
                "ValueDateTime": "0001-01-01T00:00:00",
                "Amount": 0.0,
                "ValueInt": 0,
                "UserValue1": null,
                "UserValue2": null
            }
        ]
    },
    "PortalPrivateKey": null,
    "AllAllergies": null,
    "InsurancePolicies": null,
    "WorkAddressDetails": null,
    "ResidentialAddressDetails": null,
    "PersonalContactDetails": null,
    "CustomDemographicDetails": null,
    "MembershipLevelID": "",
    "MembershipLevelName": "",
    "MembershipLevelColor": "",
    "MembershipLevelExpiryDate": "0001-01-01T00:00:00Z",
    "MembershipCount": 0,
    "MembershipStatus": 0,
    "PatientMembershipID": "",
    "AccountExecutiveName": null,
    "AccountExecutiveID": null,
    "AdvanceAmount": 0.0,
    "DueAmount": 0.0,
    "IsInactive": false,
    "InactiveReason": "",
    "InactiveDateTime": "0001-01-01T00:00:00Z",
    "IsNewPatient": true,
    "FirstAppointmentDate": "0001-01-01T00:00:00",
    "LastAppointmentDate": "0001-01-01T00:00:00",
    "NextAppointmentDate": "0001-01-01T00:00:00",
    "SavingErrorCode": null,
    "SavingErrorText": null,
    "ResponseCode": "200",
    "ResponseDetails": null,
    "CreatedDatetime": "2025-01-30T11:41:00Z",
    "ModifiedDatetime": "2025-01-30T11:41:00Z",
    "CreatedStaffName": "Admin baby MD Sandbox",
    "ModifiedStaffName": "",
    "CreatedStaffID": null,
    "ModifiedStaffID": null,
    "ID": "b09f9ef6-6fe2-4c32-bb8c-aeffb2f706a5",
    "QDID": "b09f9ef6-6fe2-4c32-bb8c-aeffb2f706a5",
    "IsSystem": false,
    "IsDeleted": false,
    "IsDirty": false,
    "UniqueSerialNo": 0,
    "IsLoadedExternally": false,
    "OrganisationID": "ff0e7efa-7aac-4872-a42e-8ad2b43569c9",
    "HeadOrganisationID": "ff0e7efa-7aac-4872-a42e-8ad2b43569c9",
    "OrganisationName": "BabyMD Sandbox",
    "LastSyncedTimeStamp": "0001-01-01T00:00:00",
    "IsNotAvailableInMyClinic": false,
    "IsNew": false,
    "DataStatus": 1,
    "DataStatusValue": "Added",
    "IsSyncedFromExternalSource": false,
    "ExternalAppSourceName": null,
    "ExternalAppSourceID": null,
}'