'use client';
import { Typography, Box, Grid, Container } from '@mui/material';
import CategoryCard from './CategoryCard';

export default function CategorySection({ title, description, items }) {
  return (
    <Box sx={{ py: 6 }}>
      <Container maxWidth="lg">
        <Typography
          variant="h4"
          component="h2"
          sx={{ mb: 2, fontWeight: 'bold' }}
        >
          {title}
        </Typography>
        <Typography
          variant="body1"
          sx={{ mb: 4, color: 'text.secondary', maxWidth: '800px' }}
        >
          {description}
        </Typography>
        <Grid container spacing={3}>
          {items.map((item, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <CategoryCard {...item} />
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
} 