import { query } from '@/app/lib/db';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

export const revalidate = 3600; // Cache for 1 hour

export async function GET(request) {
  try {
    const letter = request.nextUrl.pathname.split('/').pop();
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const offset = (page - 1) * limit;
    const languageId = languageMap[lang] || 1;

    // Get total count
    const countResult = await query(
      `SELECT 
    COUNT(*) 
FROM 
    blogs_translations bt
LEFT JOIN 
    blogs_translations default_bt ON bt.id = default_bt.id AND default_bt.language_id = 1
WHERE 
    (CASE WHEN length(coalesce(bt.title, '')) = 0 THEN default_bt.title ELSE bt.title END) ILIKE $1
    AND bt.language_id = $2`,
      [`${letter}%`, languageId]
    );
    const totalCount = parseInt(countResult.rows[0].count);

    // Get paginated blogs
    logger.info('Executing query with params:', { letter, limit, offset });
    const blogsResult = await query(
      `SELECT 
    bt.id, 
    CASE WHEN length(coalesce(bt.title, '')) = 0 THEN default_bt.title ELSE bt.title END as title, 
    bt.handle as slug, 
    bt.created_at, 
    bt.updated_at, 
    bt.published_at, 
    COALESCE(bt.author, default_bt.author) as author, 
    COALESCE(bt.body_html, default_bt.body_html) as body_html, 
    COALESCE(bt.tags, default_bt.tags) as tags, 
    COALESCE(bt.blog_title, default_bt.blog_title) as blog_title
FROM 
    blogs_translations bt
LEFT JOIN 
    blogs_translations default_bt ON bt.id = default_bt.id AND default_bt.language_id = 1
WHERE 
    (CASE WHEN length(coalesce(bt.title, '')) = 0 THEN default_bt.title ELSE bt.title END) ILIKE $1
    AND bt.language_id = $2
ORDER BY 
    bt.published_at DESC
LIMIT $3 OFFSET $4`,
      [`${letter}%`,languageId, limit, offset]
    );
    logger.info('Query result:', blogsResult.rows);

    // Format the results to match the expected structure
    const items = blogsResult.rows.map(blog => ({
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      created_at: blog.created_at,
      updated_at: blog.updated_at,
      published_at: blog.published_at,
      author: blog.author,
      body_html: blog.body_html,
      tags: blog.tags,
      blogTitle: blog.blog_title
    }));

    return new Response(JSON.stringify({
      items,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400'
      }
    });

  } catch (error) {
    logger.error('Error:', error);
    return Response.json(
      { error: 'Failed to load blogs' },
      { status: 500 }
    );
  }
}
