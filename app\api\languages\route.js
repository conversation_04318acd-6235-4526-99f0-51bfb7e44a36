import { Pool } from 'pg';
import { NextResponse } from 'next/server';
const logger = require('../../utils/logger');

const pool = new Pool({
  host: process.env.POSTGRES_HOST,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DB,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  ssl: {
    rejectUnauthorized: false,
  },
  connectionTimeoutMillis: 10000,
  idleTimeoutMillis: 30000,
});

// 👇 Add CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Better to use specific domain
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

export async function GET() {
  let client;
  try {
    client = await pool.connect();

    const { rows } = await client.query(
      'SELECT code, name_in_local_script FROM languages ORDER BY name_in_local_script'
    );

    return new NextResponse(JSON.stringify(rows), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });
  } catch (error) {
    logger.error('Database error:', {
      message: error.message,
      code: error.code,
      stack: error.stack,
    });

    return new NextResponse(
      JSON.stringify({ error: `Database error: ${error.message}` }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
}

// 👇 Handle preflight OPTIONS request (important!)
export function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}
