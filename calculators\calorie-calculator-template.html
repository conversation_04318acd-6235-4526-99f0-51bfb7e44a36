<!DOCTYPE html>
<html lang="{{lang}}">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>{{title}}</title>
    <meta name="description" content="{{meta_description}}" />
    <meta name="keywords" content="{{meta_keywords}}" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="{{og_title}}" />
    <meta property="og:description" content="{{og_description}}" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{twitter_title}}" />
    <meta name="twitter:description" content="{{twitter_description}}" />
    <link rel="canonical" href="{{canonical_url}}" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/{{lang}}/calculators/calorie-calculator" />
    <link rel="icon" href="/{{lang}}/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/{{lang}}/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img
            width="200"
            src="/{{lang}}/calculators/assets/august_logo_green_nd4fn9.svg"
            alt="{{logo_alt}}"
          />
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select" style="border:1px solid #e5e7eb;border-radius:6px;padding:6px 8px;font-size:14px;color:#374151;background:#fff;outline:none;">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="{{talk_to_august_url}}"
            class="talk-to-august"
            >{{talk_to_august}}</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>{{main_title}}</h1>
        <p>{{main_description}}</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">{{us_units}}</button>
            <button class="tab-button active" data-unit="metric">{{metric_units}}</button>
          </div>
          <p class="form-instruction">{{form_instruction}}</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">{{age_label}}</label>
              <input type="text" id="age" value="25" />
              <span>{{age_range}}</span>
            </div>
            <div class="form-field">
              <label>{{gender_label}}</label>
              <input type="radio" name="gender" value="male" checked /> {{male}}
              <input type="radio" name="gender" value="female" /> {{female}}
            </div>
            <div class="form-field">
              <label for="height">{{height_label}}</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>{{feet}}</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>{{inches}}</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>{{cm}}</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">{{weight_label}}</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>{{pounds}}</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>{{kg}}</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">{{activity_label}}</label>
              <select id="activity">
                <option value="sedentary">{{sedentary_option}}</option>
                <option value="light">{{light_option}}</option>
                <option value="moderate" selected>{{moderate_option}}</option>
                <option value="very">{{very_active_option}}</option>
                <option value="super">{{super_active_option}}</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">{{settings_link}}</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>{{settings_header}}</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>{{results_unit_label}}</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">{{calories_unit}}</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">{{kilojoules_unit}}</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  {{body_fat_percentage_label}}
                  <span
                    class="info-icon"
                    title="{{body_fat_percentage_info}}"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  {{bmr_estimation_formula_label}}
                  <span
                    class="info-icon"
                    title="{{bmr_estimation_formula_info}}"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">{{mifflin_formula}}</label>
                  </div |cutoff|>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">{{harris_formula}}</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">{{katch_formula}}</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                {{calculate_button}}
              </button>
              <button type="button" class="clear-button">{{clear_button}}</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>{{results_header}}</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="{{download_results_pdf}}"
            >
              <span class="download-icon">📥</span>
              <span>{{download_pdf}}</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">{{results_description}}</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                {{bmr_label}}
                <span id="bmr-value" style="color: #111827">1,650</span>
                {{calories_per_day}}
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  {{activity_levels_label}}
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>{{sedentary_label}}:</strong> {{sedentary_description}}
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>{{light_label}}:</strong> {{light_description}}
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>{{moderate_label}}:</strong> {{moderate_description}}
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>{{active_label}}:</strong> {{active_description}}
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>{{very_active_label}}:</strong> {{very_active_description}}
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>{{extra_active_label}}:</strong> {{extra_active_description}}
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">{{goal_label}}</th>
                  <th style="width: 30%">{{calories_label}}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="{{goal_label}}">
                    <div class="goal-label">{{maintain_weight}}</div>
                  </td>
                  <td data-label="{{daily_calories_label}}">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">{{calories_per_day}}</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="{{goal_label}}">
                    <div class="goal-label">{{mild_weight_loss}}</div>
                    <div class="goal-description">{{mild_weight_loss_rate}}</div>
                  </td>
                  <td data-label="{{daily_calories_label}}">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">{{calories_per_day}}</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="{{goal_label}}">
                    <div class="goal-label">{{weight_loss}}</div>
                    <div class="goal-description">{{weight_loss_rate}}</div>
                  </td>
                  <td data-label="{{daily_calories_label}}">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">{{calories_per_day}}</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="{{goal_label}}">
                    <div class="goal-label">{{extreme_weight_loss}}</div>
                    <div class="goal-description">{{extreme_weight_loss_rate}}</div>
                  </td>
                  <td data-label="{{daily_calories_label}}">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">{{calories_per_day}}</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >{{show_weight_gain_info}}</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                {{weight_gain_info}}
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">{{goal_label}}</th>
                    <th style="width: 30%">{{calories_label}}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="{{goal_label}}">
                      <div class="goal-label">{{mild_weight_gain}}</div>
                      <div class="goal-description">{{mild_weight_gain_rate}}</div>
                    </td>
                    <td data-label="{{daily_calories_label}}">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">{{calories_per_day}}</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="{{goal_label}}">
                      <div class="goal-label">{{weight_gain}}</div>
                      <div class="goal-description">{{weight_gain_rate}}</div>
                    </td>
                    <td data-label="{{daily_calories_label}}">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">{{calories_per_day}}</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="{{goal_label}}">
                      <div class="goal-label">{{fast_weight_gain}}</div>
                      <div class="goal-description">{{fast_weight_gain_rate}}</div>
                    </td>
                    <td data-label="{{daily_calories_label}}">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">{{calories_per_day}}</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>{{physical_activity_guidelines}}</h2>
          <ul>
            <li>
              <strong>{{light_exercise_label}}:</strong> {{light_exercise_description}}
            </li>
            <li>
              <strong>{{moderate_exercise_label}}:</strong> {{moderate_exercise_description}}
            </li>
            <li>
              <strong>{{vigorous_exercise_label}}:</strong> {{vigorous_exercise_description}}
            </li>
            <li>
              <strong>{{professional_training_label}}:</strong> {{professional_training_description}}
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>{{nutritional_reference_guide}}</h2>
          <p>{{nutritional_reference_description}}</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              {{caloric_content_popular_foods}}
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{food_item_label}}
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{portion_size_label}}
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{calories_label}}
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{kj_label}}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{fresh_fruits_label}}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{mango}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{mango_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{kiwi}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{kiwi_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{blueberries}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{blueberries_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{avocado}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{avocado_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{cherries}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{cherries_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{fresh_vegetables_label}}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{sweet_potato}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{sweet_potato_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{bell_pepper}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{bell_pepper_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{spinach}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{spinach_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{zucchini}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{zucchini_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{cauliflower}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{cauliflower_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{protein_sources_label}}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{salmon_grilled}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{salmon_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{turkey_breast}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{turkey_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{greek_yogurt}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{greek_yogurt_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{almonds}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{almonds_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{lentils_cooked}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{lentils_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{grains_starches_label}}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{quinoa_cooked}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{quinoa_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{oatmeal}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{oatmeal_portion}}
                    </td>
                    <td style="padding: 8/*cutoff*/px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{whole_wheat_pasta}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{whole_wheat_pasta_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{brown_rice}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{brown_rice_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{beverages_label}}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{green_tea}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{green_tea_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{almond_milk}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{almond_milk_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{coconut_water}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{coconut_water_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{red_wine}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      {{red_wine_portion}}
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              {{calorie_table_note}}
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              {{strategic_meal_planning}}
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{meal_period_label}}
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{cal_1300_plan}}
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{cal_1600_plan}}
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      {{cal_2100_plan}}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      {{breakfast_label}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{breakfast_1300}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{breakfast_1600}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{breakfast_2100}}
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{morning_snack_label}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{morning_snack_1300}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{morning_snack_1600}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{morning_snack_2100}}
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      {{total_morning_label}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_morning_1300}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_morning_1600}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_morning_2100}}
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      {{lunch_label}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{lunch_1300}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{lunch_1600}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{lunch_2100}}
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{afternoon_snack_label}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{afternoon_snack_1300}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{afternoon_snack_1600}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{afternoon_snack_2100}}
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      {{total_midday_label}}
                    </td>
                    <td
.PerformAction("cutoff") style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_midday_1300}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_midday_1600}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_midday_2100}}
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      {{dinner_label}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{dinner_1300}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{dinner_1600}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{dinner_2100}}
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{evening_snack_label}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{evening_snack_1300}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{evening_snack_1600}}
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      {{evening_snack_2100}}
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      {{total_evening_label}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_evening_1300}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_evening_1600}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{total_evening_2100}}
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{daily_total_label}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{daily_total_1300}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{daily_total_1600}}
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      {{daily_total_2100}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>{{master_your_metabolism}}</h2>
          <p>{{metabolism_description}}</p>

          <div class="equations-container">
            <h3>{{three_proven_formulas}}</h3>
            <p>{{formulas_description}}</p>

            <div class="equation-card">
              <h4>{{mifflin_st_jeor_title}}</h4>
              <p>
                <strong>{{male_formula_label}}:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>{{female_formula_label}}:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                {{mifflin_st_jeor_note}}
              </p>
            </div>

            <div class="equation-card">
              <h4>{{harris_benedict_title}}</h4>
              <p>
                <strong>{{male_calculation_label}}:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>{{female_calculation_label}}:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                {{harris_benedict_note}}
              </p>
            </div>

            <div class="equation-card">
              <h4>{{katch_mcardle_title}}</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                {{katch_mcardle_note}}
              </p>
            </div>

            <div class="info-text">
              <h3>{{tdee_transformation}}</h3>
              <p>{{tdee_description}}</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        {{activity_level_label}}
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        {{description_label}}
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        {{multiplier_label}}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        {{sedentary_label}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        {{sedentary_description}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        {{lightly_active_label}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        {{light_description}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        {{moderately_active_label}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        {{moderate_description}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        {{very_active_label}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        {{very_active_description}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        {{super_active_label}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        {{super_active_description}}
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>{{tdee_result_description}}</p>

              <h3>{{strategic_weight_management}}</h3>
              <p>{{weight_management_description}}</p>

              <div class="warning-note">
                <strong>{{critical_guidelines}}</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>{{metabolic_suppression}}</li>
                  <li>{{muscle_degradation}}</li>
                  <li>{{nutrient_depletion}}</li>
                  <li>{{rebound_weight_gain}}</li>
                </ul>
                <p style="margin-top: 12px">{{optimal_results_description}}</p>
              </div>

              <h3>{{nutrition_optimization_strategies}}</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>{{nutrient_density_priority}}</li>
                <li>{{adequate_protein_intake}}</li>
                <li>{{reject_restrictive_dieting}}</li>
                <li>{{consistent_daily_practices}}</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                {{calculator_purpose}}
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>{{precision_calorie_tracking}}</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>{{determine_metabolic_baseline}}</h4>
              <p>{{metabolic_baseline_description}}</p>
            </div>

            <div class="step-card">
              <h4>{{establish_weight_targets}}</h4>
              <p>{{weight_targets_description}}</p>
            </div>

            <div class="step-card">
              <h4>{{implement_monitoring_systems}}</h4>
              <p>{{monitoring_systems_description_1}}</p>
              <p>{{monitoring_systems_description_2}}</p>
            </div>

            <div class="step-card">
              <h4>{{optimize_through_assessment}}</h4>
              <p>{{assessment_description}}</p>
            </div>
          </div>

          <div class="info-text">
            <h3>{{caloric_balance_science}}</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>{{energy_balance_fundamentals}}:</strong> {{energy_balance_description}}</li>
              <li><strong>{{thermic_effect_food}}:</strong> {{thermic_effect_description}}</li>
              <li><strong>{{satiety_food_quality}}:</strong> {{satiety_description}}</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              {{twinkie_diet_case_study}}
            </p>

            <h4 style="color: #416955; margin-top: 20px">{{bonus_benefits_tracking}}</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>{{builds_nutritional_awareness}}</li>
              <li>{{improves_portion_control}}</li>
              <li>{{connects_food_exercise}}</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>{{zigzag_calorie_cycling}}</h2>
          <p>{{zigzag_description}}</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>{{example_label}}</h4>
              <p><strong>{{weekly_target_label}}:</strong> {{weekly_target_value}}</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>{{option_a_label}}:</strong> {{option_a_description}}</li>
                <li><strong>{{option_b_label}}:</strong> {{option_b_description}}</li>
              </ul>
              <p>{{zigzag_adjustment_description}}</p>
            </div>

            <div class="benefits-card">
              <h4>{{zigzag_goal_label}}</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>{{better_metabolic_flexibility}}</li>
                <li>{{diet_flexibility}}</li>
                <li>{{prevents_metabolic_adaptation}}</li>
                <li>{{breaks_weight_loss_plateaus}}</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>{{calorie_requirements_title}}</h2>
          <p>{{calorie_needs_vary_by}}</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>{{factors_influence_calorie_needs}}</h4>
              <ul>
                <li>{{age_sex_weight_height}}</li>
                <li>{{activity_level}}</li>
                <li>{{health_status}}</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>{{general_guidelines_label}}</h4>
              <p><strong>{{men_label}}:</strong> {{men_calorie_range}}</p>
              <p><strong>{{women_label}}:</strong> {{women_calorie_range}}</p>
            </div>

            <div class="minimum-card">
              <h4>{{minimum_safe_intake}}</h4>
              <p><strong>{{women_label}}:</strong> {{women_minimum_intake}}</p>
              <p><strong>{{men_label}}:</strong> {{men_minimum_intake}}</p>
              <p class="warning">{{medically_supervised_warning}}</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>{{over_restricting_warning}}</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>{{not_all_calories_equal}}</h2>
          <p>{{calories_come_from}}</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>{{protein}}</li>
            <li>{{carbohydrates}}</li>
            <li>{{fat}}</li>
            <li>{{alcoholl}}</li>
          </ul>

          <p>{{nutrition_labels_accuracy}}</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>{{high_calorie_foods}}</h4>
              <p>{{high_calorie_description}}</p>
              <ul>
                <li>{{avocados}}</li>
                <li>{{nuts_and_seeds}}</li>
                <li>{{fried_foods}}</li>
                <li>{{sugary_foods}}</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>{{low_calorie_foods}}</h4>
              <ul>
                <li>{{many_vegetables}}</li>
                <li>{{some_fruits}}</li>
                <li>{{lean_proteins}}</li>
                <li>{{whole_grains}}</li>
                <li>{{leafy_greens}}</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>{{empty_calories}}</h4>
              <ul>
                <li>{{sugary_drinks}}</li>
                <li>{{processed_snacks}}</li>
                <li>{{added_sugars}}</li>
                <li>{{solid_fats}}</li>
                <li>{{alcohol}}</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>{{caloric_quality_matters}}</h3>
            <p>{{drinks_calorie_impact}}</p>

            <h4 style="color: #416955; margin-top: 16px">{{build_balanced_plan}}</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>{{focus_whole_foods}}</li>
              <li>{{limit_sugar_snacks}}</li>
              <li>{{natural_portion_control}}</li>
              <li>{{combine_calorie_counting_exercise}}</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>{{final_takeaway}}</h2>
          <p>{{no_one_size_fits_all}}</p>
          <p style="font-weight: 500; color: #416955">{{track_wisely_eat_mindfully}}</p>
        </section>
      </main>
    </div>
    <script src="/{{lang}}/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>