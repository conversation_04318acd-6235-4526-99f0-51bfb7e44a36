import { Nunito_Sans } from "next/font/google";
import "./globals.css";
import Script from 'next/script';
import ClientProviders from './components/ClientProviders';

const nunitoSans = Nunito_Sans({
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  variable: '--font-nunito-sans',
  fallback: ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
});


export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        {/* Google Tag Manager */}
        <Script id="gtm-script" strategy="afterInteractive">
          {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NG4XS3LL');`}
        </Script>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://res.cloudinary.com" />
        <link rel="dns-prefetch" href="https://res.cloudinary.com" />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/august_logo_green.svg" as="image" type="image/svg+xml" />
        
        {/* Critical CSS - inline minimal styles for immediate render */}
        <style dangerouslySetInnerHTML={{
          __html: `
            :root {
              --font-nunito-sans: ${nunitoSans.style.fontFamily};
            }
            body {
              font-family: var(--font-nunito-sans), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 0;
              background-color: #fff;
              color: #171717;
              line-height: 1.6;
              -webkit-font-smoothing: antialiased;
              -moz-osx-font-smoothing: grayscale;
            }
            * {
              box-sizing: border-box;
            }
            .MuiTypography-root {
              font-family: inherit;
            }
            /* Critical above-the-fold styles */
            .MuiAppBar-root {
              background-color: #fff !important;
              color: #111 !important;
              box-shadow: none !important;
              border-bottom: 1px solid #e0e0e0;
            }
            .MuiToolbar-root {
              min-height: 64px;
              padding: 0 16px;
            }
            /* Hero section critical styles */
            .hero-section {
              background-color: #F7F6F4;
              padding: 48px 0;
            }
            /* Prevent layout shift */
            img {
              max-width: 100%;
              height: auto;
            }
          `
        }} />
      </head>
      
      <body className={nunitoSans.className}>
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NG4XS3LL" height="0" width="0" style={{display:'none',visibility:'hidden'}}></iframe>
        </noscript>
        {/* Use client component for providers */}
        <ClientProviders>
          {children}
        </ClientProviders>
        
        {/* Analytics - load after LCP with delay */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-W3RS4004JN"
          strategy="lazyOnload"
        />
        <Script id="google-analytics" strategy="lazyOnload">
          {`
            // Delay analytics initialization to not block LCP
            setTimeout(() => {
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-W3RS4004JN');
            }, 3000);
          `}
        </Script>
      </body>
    </html>
  );
}
