<!DOCTYPE html>
<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>1日のカロリー必要量計算機 - 正確なBMR・TDEE計算機 | MeetAugust</title>
    <meta name="description" content="高度なBMR計算機で1日のカロリー必要量を計算しましょう。減量、維持、筋肉増強のためのパーソナライズされたカロリー目標を取得。無料、正確、科学的根拠に基づいています。" />
    <meta name="keywords" content="カロリー計算機、BMR計算機、TDEE計算機、1日のカロリー必要量、減量計算機、代謝計算機、栄養計画" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="1日のカロリー必要量計算機 - 正確なBMR・TDEE計算機" />
    <meta property="og:description" content="高度なBMR計算機で1日のカロリー必要量を計算しましょう。減量、維持、筋肉増強のためのパーソナライズされたカロリー目標を取得。" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="1日のカロリー必要量計算機 - 正確なBMR・TDEE計算機" />
    <meta name="twitter:description" content="高度なBMR計算機で1日のカロリー必要量を計算しましょう。減量、維持、筋肉増強のためのパーソナライズされたカロリー目標を取得。" />
    <link rel="canonical" href="https://www.meetaugust.ai/calculator/calorie" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/ja/calculators/calorie-calculator" />
    <link rel="icon" href="/ja/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/ja/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img
            width="200"
            src="/ja/calculators/assets/august_logo_green_nd4fn9.svg"
            alt="計算機ロゴ"
          />
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select" style="border:1px solid #e5e7eb;border-radius:6px;padding:6px 8px;font-size:14px;color:#374151;background:#fff;outline:none;">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/redirect/wa?message=Hello%20August"
            class="talk-to-august"
            >Augustと話す</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>高度な1日カロリー必要量計算機</h1>
        <p>正確なカロリー計算機で1日に必要なカロリー量を調べましょう。減量、筋肉増強、<br /> または健康的なライフスタイルの維持に最適で、あなたの体、目標、活動レベルに基づいています。</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">米国単位</button>
            <button class="tab-button active" data-unit="metric">メートル法単位</button>
          </div>
          <p class="form-instruction">下記に個人情報を入力し、計算をクリックしてパーソナライズされたカロリー推奨事項を取得してください</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">年齢</label>
              <input type="text" id="age" value="25" />
              <span>年齢15 - 80</span>
            </div>
            <div class="form-field">
              <label>性別</label>
              <input type="radio" name="gender" value="male" checked /> 男性
              <input type="radio" name="gender" value="female" /> 女性
            </div>
            <div class="form-field">
              <label for="height">身長</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>フィート</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>インチ</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">体重</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>ポンド</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">活動</label>
              <select id="activity">
                <option value="sedentary">座りがち：運動はほとんどまたはなし</option>
                <option value="light">軽度活動：軽い運動を週1-3日</option>
                <option value="moderate" selected>中程度活動：中程度の運動を週3-5日</option>
                <option value="very">非常に活動的：激しい運動を週6-7日</option>
                <option value="super">超活動的：非常に激しい運動、肉体労働</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ 設定</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>設定</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>結果の単位：</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">カロリー</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">キロジュール</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  体脂肪率：
                  <span
                    class="info-icon"
                    title="より正確な体組成計算のために体脂肪率を入力してください"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  BMR推定式：
                  <span
                    class="info-icon"
                    title="基礎代謝率を計算するための式を選択してください"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div |cutoff|>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">改訂Harris-Benedict</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                計算 ▶
              </button>
              <button type="button" class="clear-button">クリア</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>結果</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="結果をPDFでダウンロード"
            >
              <span class="download-icon">📥</span>
              <span>PDFをダウンロード</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">あなたのパーソナライズされたカロリー目標は高度な代謝式を使用して計算されます。これらの推奨事項は、あなたの特定の目標に合わせた1日のカロリー摂取ガイドラインを提供します - 現在の体重を維持したい、持続可能な減量を達成したい、または健康的な体重増加をサポートしたい場合。</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                基礎代謝率（BMR）：
                <span id="bmr-value" style="color: #111827">1,650</span>
                カロリー/日
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  活動レベル：
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>座りがち:</strong> 運動はほとんどまたはなし
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>軽度:</strong> 週1-3回の運動
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>中程度:</strong> 週4-5回の運動
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>活動的:</strong> 毎日の運動または週3-4回の激しい運動
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>非常に活動的:</strong> 週6-7回の激しい運動
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>超活動的:</strong> 毎日非常に激しい運動、または肉体労働
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">目標</th>
                  <th style="width: 30%">カロリー</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="目標">
                    <div class="goal-label">体重維持</div>
                  </td>
                  <td data-label="1日のカロリー">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">カロリー/日</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="目標">
                    <div class="goal-label">軽度の減量</div>
                    <div class="goal-description">週0.5ポンド</div>
                  </td>
                  <td data-label="1日のカロリー">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">カロリー/日</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="目標">
                    <div class="goal-label">減量</div>
                    <div class="goal-description">週1ポンド</div>
                  </td>
                  <td data-label="1日のカロリー">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">カロリー/日</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="目標">
                    <div class="goal-label">極端な減量</div>
                    <div class="goal-description">週2ポンド</div>
                  </td>
                  <td data-label="1日のカロリー">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">カロリー/日</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >体重増加の情報を表示</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                体重増加情報
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">目標</th>
                    <th style="width: 30%">カロリー</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="目標">
                      <div class="goal-label">軽度の体重増加</div>
                      <div class="goal-description">週0.25kg</div>
                    </td>
                    <td data-label="1日のカロリー">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">カロリー/日</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="目標">
                      <div class="goal-label">体重増加</div>
                      <div class="goal-description">週0.5kg</div>
                    </td>
                    <td data-label="1日のカロリー">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">カロリー/日</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="目標">
                      <div class="goal-label">急速な体重増加</div>
                      <div class="goal-description">週1kg</div>
                    </td>
                    <td data-label="1日のカロリー">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">カロリー/日</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>身体活動ガイドライン</h2>
          <ul>
            <li>
              <strong>軽い運動:</strong> ウォーキングや軽いヨガなどの中程度の強度の活動を20-40分。
            </li>
            <li>
              <strong>中程度の運動:</strong> 速歩きやサイクリングなど心拍数を上げる活動を30-60分。
            </li>
            <li>
              <strong>激しい運動:</strong> 高強度トレーニング、スポーツ、または要求の高い身体活動を45-90分。
            </li>
            <li>
              <strong>プロフェッショナル/アスリートトレーニング:</strong> 集中的なトレーニングまたは肉体労働を2時間以上。
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>完全栄養参考ガイド</h2>
          <p>これらの包括的な表を使用して、情報に基づいた食事選択を行い、日常食品のカロリー含有量、食事計画戦略、運動エネルギー消費をよりよく理解してください。</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              人気食品のカロリー含有量
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      食品
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      分量
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      カロリー
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      新鮮な果物
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      マンゴー
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      中1個（5オンス）
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      キウイ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      大1個（3オンス）
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ブルーベリー
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      アボカド
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      中1/2個（3オンス）
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      チェリー
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      新鮮な野菜
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      サツマイモ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      中1個（5オンス）
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ピーマン
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      スライス1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ほうれん草
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      生2カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ズッキーニ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      スライス1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      カリフラワー
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      タンパク質源
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      サーモン、グリル
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3オンス
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ターキー胸肉
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3オンス
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ギリシャヨーグルト
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      6オンス容器
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      アーモンド
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1オンス（23個）
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      レンズ豆、調理済み
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      穀物・でんぷん
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      キヌア、調理済み
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      オートミール
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      調理済み1カップ
                    </td>
                    <td style="padding: 8/*cutoff*/px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      全粒小麦パスタ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      調理済み1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      玄米
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      調理済み1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      飲み物
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      緑茶
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      アーモンドミルク
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      ココナッツウォーター
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1カップ
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      赤ワイン
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5オンス
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              注：カロリー値は概算で、調理方法や特定のブランドによって異なる場合があります。
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              戦略的食事計画
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      食事時間
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      1,300カロリープラン
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      1,600カロリープラン
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      2,100カロリープラン
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      朝食
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ギリシャヨーグルト1/2カップとブルーベリー1/2カップ（130カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      オートミール1カップとブルーベリー1/2カップ（238カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      オートミール1カップとキウイ1個、アーモンド1オンス（458カロリー）
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      朝の間食
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      小キウイ1個（56カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      中マンゴー1個（135カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      中マンゴー1個、チェリー10個（232カロリー）
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      朝の合計
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      186カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      373カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      690カロリー
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      昼食
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ほうれん草サラダ2カップとグリルサーモン3オンス（189カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ターキー胸肉3オンス、ズッキーニ1カップ、キヌア1/2カップ（264カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      グリルサーモン3オンス、玄米1カップ、カリフラワー1カップ（418カロリー）
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      午後の間食
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ピーマンスライス1カップ（28カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      アボカド1/2個（160カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      アボカド1/2個、アーモンド1オンス（324カロリー）
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      昼の合計
                    </td>
                    <td
.PerformAction("cutoff") style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      217カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      424カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      742カロリー
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      夕食
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ターキー胸肉3オンス、カリフラワー1カップ（150カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      グリルサーモン3オンス、サツマイモ1カップ（287カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ターキー胸肉3オンス、全粒小麦パスタ1カップ、ほうれん草1カップ（313カロリー）
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      夜の間食
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      緑茶1カップ（2カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ココナッツウォーター1カップ（46カロリー）
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      ギリシャヨーグルト1カップ（130カロリー）
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      夜の合計
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      152カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      333カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      443カロリー
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1日の合計
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,255カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,630カロリー
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2,175カロリー
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>代謝をマスターする</h2>
          <p>代謝を理解することは健康目標を達成する鍵です。この計算機は科学的に検証された式を使用して基礎代謝率（BMR）と1日の総エネルギー消費量（TDEE）を推定します。</p>

          <div class="equations-container">
            <h3>BMRの3つの実証された式</h3>
            <p>この計算機は、あなたのBMRを推定するために3つのよく研究された方程式を使用し、それぞれがあなたのプロフィールに応じてユニークな強みを持っています：</p>

            <div class="equation-card">
              <h4>Mifflin-St Jeor式</h4>
              <p>
                <strong>男性:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>女性:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                一般人口、特に非アスリートにとって最も正確と広く考えられています。
              </p>
            </div>

            <div class="equation-card">
              <h4>改訂Harris-Benedict式</h4>
              <p>
                <strong>男性:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>女性:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                現代の正確性のために更新された信頼できる式で、幅広い個人に適しています。
              </p>
            </div>

            <div class="equation-card">
              <h4>Katch-McArdle式</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                除脂肪体重を考慮するため、体脂肪率がわかっている個人に理想的です。
              </p>
            </div>

            <div class="info-text">
              <h3>TDEE：BMRを実行可能な目標に変換</h3>
              <p>あなたの1日の総エネルギー消費量（TDEE）は、あなたのライフスタイルを反映する活動係数を掛けたBMRです。これにより、1日のカロリー必要量の完全な絵が得られます。</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        活動レベル
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        説明
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        乗数
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        座りがち
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        運動はほとんどまたはなし
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        軽度活動
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        週1-3回の運動
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        中程度活動
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        週4-5回の運動
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        非常に活動的
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        週6-7回の激しい運動
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        超活動的
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        毎日非常に激しい運動、または肉体労働
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>結果は、あなたが毎日消費するカロリーの正確な推定値で、減量、維持、または増量のために食事を調整するために使用できます。</p>

              <h3>戦略的体重管理</h3>
              <p>減量にはカロリー不足が必要で、増量には余剰が必要です。この計算機は各目標の正確なカロリー目標を提供し、持続可能な進歩を保証します。</p>

              <div class="warning-note">
                <strong>安全な減量のための重要なガイドライン：</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>代謝を遅くすることを防ぐために極端な不足を避けてください。</li>
                  <li>筋肉量を維持するために十分なタンパク質を確保してください。</li>
                  <li>不足を避けるために栄養豊富な食品を優先してください。</li>
                  <li>リバウンドを防ぐためにクラッシュダイエットを避けてください。</li>
                </ul>
                <p style="margin-top: 12px">最適な結果のために、栄養士やダイエット専門家に相談してプランをさらにパーソナライズしてください。</p>
              </div>

              <h3>栄養最適化戦略</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>野菜、低脂肪タンパク質、全粒穀物などの栄養豊富な食品を優先してください。</li>
                <li>筋肉維持と満腹感をサポートするために十分なタンパク質摂取を確保してください。</li>
                <li>持続可能でバランスの取れた食事を支持して、過度に制限的なダイエットを拒否してください。</li>
                <li>長期的な健康的な習慣を構築するために、摂取量を一貫して追跡してください。</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                この計算機は出発点です。進歩に基づいて調整し、パーソナライズされたガイダンスのために専門家に相談してください。
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>精密カロリートラッキング</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>ステップ1：代謝ベースラインを決定する</h4>
              <p>この計算機を使用して、年齢、性別、体重、身長、活動レベルに基づいてBMRとTDEEを見つけてください。</p>
            </div>

            <div class="step-card">
              <h4>ステップ2：体重目標を確立する</h4>
              <p>提供されたカロリー推奨事項を使用して、減量、維持、または増量の現実的な目標を設定してください。</p>
            </div>

            <div class="step-card">
              <h4>ステップ3：モニタリングシステムを実装する</h4>
              <p>アプリや食事日記を使用してカロリー摂取量を追跡し、目標に合わせてください。</p>
              <p>週に1回体重を測り、日々の変動ではなく傾向を監視してください。</p>
            </div>

            <div class="step-card">
              <h4>ステップ4：評価を通じて最適化する</h4>
              <p>プランを効果的に保つために、4-6週間ごと、または重要な体重変化後にカロリー必要量を再評価してください。</p>
            </div>
          </div>

          <div class="info-text">
            <h3>カロリーバランスの科学</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>エネルギーバランスの基礎:</strong> 体重管理は摂取カロリー対消費カロリーによって支配されます。</li>
              <li><strong>食物の熱効果:</strong> 異なる食品は消化に異なる量のエネルギーを必要とし、総カロリー消費に影響します。</li>
              <li><strong>満腹感と食品の質:</strong> 高繊維、高タンパク質の食品は満腹感を促進し、プランを守るのに役立ちます。</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              例：「ツインキーダイエット」は、カロリー不足で減量が可能であることを示しましたが、栄養不足のダイエットは長期的な健康に害を与えます。
            </p>

            <h4 style="color: #416955; margin-top: 20px">カロリートラッキングのボーナス効果</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>栄養意識とマインドフルな食事習慣を構築します。</li>
              <li>一貫したトラッキングを通じてポーションコントロールを改善します。</li>
              <li>食品選択と運動を結びつけてエネルギーバランスを最適化します。</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>ジグザグカロリーサイクリング</h2>
          <p>ジグザグカロリーサイクリングは、週の目標を維持しながら毎日のカロリー摂取量を変えることで、代謝の柔軟性を向上させ、プラトーを防ぎます。</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>例</h4>
              <p><strong>週の目標:</strong> 14,000カロリー（1日平均2,000カロリー）</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>オプションA:</strong> 2,000カロリーで7日間。</li>
                <li><strong>オプションB:</strong> 1,800カロリーで5日間、2,500カロリーで2日間。</li>
              </ul>
              <p>両方のオプションは週の目標を達成しますが、代謝を動的に保つために毎日の摂取量を変えます。</p>
            </div>

            <div class="benefits-card">
              <h4>ジグザグ目標</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>より良い代謝の柔軟性と順守。</li>
                <li>特に社交イベントのための食事計画により多くの柔軟性。</li>
                <li>長期的な不足からの代謝適応を防ぎます。</li>
                <li>カロリー摂取量を変えることで減量プラトーを突破します。</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>ライフスタイル別カロリー必要量</h2>
          <p>カロリー必要量は個人要因によって異なりますが、一般的なガイドラインが開始に役立ちます。</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>カロリー必要量に影響する要因</h4>
              <ul>
                <li>年齢、性別、体重、身長。</li>
                <li>活動レベル（座りがちから非常に活動的まで）。</li>
                <li>妊娠や医療状態を含む健康状態。</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>一般的なガイドライン</h4>
              <p><strong>男性:</strong> 1日2,000–3,000カロリー</p>
              <p><strong>女性:</strong> 1日1,600–2,400カロリー</p>
            </div>

            <div class="minimum-card">
              <h4>最小安全摂取量</h4>
              <p><strong>女性:</strong> 1日1,200カロリー</p>
              <p><strong>男性:</strong> 1日1,500カロリー</p>
              <p class="warning">これらのレベルを下回る摂取量は医学的監督が必要です。</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>カロリーを過度に制限すると、栄養不足、筋肉減少、代謝低下につながる可能性があります。常に健康を優先してください。</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>すべてのカロリーが同じではない</h2>
          <p>カロリーは異なるマクロ栄養素から来ており、それぞれが体にユニークな効果があります：</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>タンパク質：1グラムあたり4カロリー - 筋肉修復と満腹感をサポート。</li>
            <li>炭水化物：1グラムあたり4カロリー - 主要なエネルギー源。</li>
            <li>脂肪：1グラムあたり9カロリー - ホルモンと栄養吸収に不可欠。</li>
            <li>アルコール：1グラムあたり7カロリー - 最小限の栄養価。</li>
          </ul>

          <p>栄養ラベルは正確なカロリー数を提供しますが、分量と調理方法が重要です。</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>高カロリー食品</h4>
              <p>脂肪や糖分のため、カロリーが濃い。体重管理には控えめに使用。</p>
              <ul>
                <li>アボカド、油。</li>
                <li>ナッツと種。</li>
                <li>揚げ物。</li>
                <li>甘いデザートとスナック。</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>低カロリー食品</h4>
              <ul>
                <li>多くの野菜（例：ほうれん草、ズッキーニ）。</li>
                <li>一部の果物（例：ベリー）。</li>
                <li>低脂肪タンパク質（例：ターキー、魚）。</li>
                <li>適度な全粒穀物。</li>
                <li>ボリュームと栄養のための葉物野菜。</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>空のカロリー</h4>
              <ul>
                <li>甘い飲み物（例：ソーダ）。</li>
                <li>加工スナック（例：チップス、クッキー）。</li>
                <li>包装食品の添加糖。</li>
                <li>固形脂肪（例：バター、マーガリン）。</li>
                <li>最小限の栄養効果のアルコール。</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>なぜカロリーの質が重要か</h3>
            <p>ソーダやアルコールなどの飲み物は満腹感なしにカロリーを追加し、不足を維持することを困難にします。</p>

            <h4 style="color: #416955; margin-top: 16px">バランスの取れたプランを構築する</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>栄養密度のために、加工されていない全食品に焦点を当ててください。</li>
              <li>甘いスナックと飲み物を制限してください。</li>
              <li>自然なポーションコントロールのために高繊維、高タンパク質食品を使用してください。</li>
              <li>持続可能な結果のためにカロリーカウンティングと運動を組み合わせてください。</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>最終的な要点</h2>
          <p>栄養に万能なアプローチはありません。この計算機を出発点として使用し、進歩を追跡し、ユニークなニーズを満たすために必要に応じて調整してください。</p>
          <p style="font-weight: 500; color: #416955">賢く追跡し、マインドフルに食べ、長期的な健康を優先してください。</p>
        </section>
      </main>
    </div>
    <script src="/ja/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>