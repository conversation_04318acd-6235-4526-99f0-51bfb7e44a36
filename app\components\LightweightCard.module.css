.card {
  height: 100%;
  transition: transform 0.2s ease;
  border-radius: 16px;
  background-color: #F7F6F4;
  padding: 24px 16px;
  box-shadow: none;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cardContent {
  padding: 0;
}

.cardContent h3 {
  font-weight: 500;
  margin: 0 0 8px 0;
  font-size: 1.25rem;
  line-height: 1.6;
}

.cardContent p {
  margin: 0;
  color: #666;
  font-size: 0.875rem;
  line-height: 1.43;
}

/* Featured blog cards */
.cardContent .blogTitle {
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
  margin-bottom: 16px;
  font-size: 1.25rem;
}

.cardContent .blogDescription {
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 0.875rem;
  line-height: 1.43;
}
