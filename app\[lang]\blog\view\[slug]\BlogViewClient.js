'use client';

import React, { useEffect, useState } from 'react';
import NavBar from '../../../../components/NavBar';
import { useLanguageStrings } from '@/app/contexts/LanguageStringsContext';
import ContentSection from '../../../../components/ContentSection';
import { Box, Container, Typography, Breadcrumbs, Link as MuiLink, CircularProgress } from '@mui/material';
import ShareButton from '../../../../components/shared/ShareButton';
import Link from 'next/link';
import Footer from '../../../../components/Footer';
import { Helmet } from 'react-helmet-async';
import { getRedirectPath } from '@/app/utils/getRedirectPath';
import reviewedByTranslations from '@/app/contexts/ReviewedByTranslationStrings';
import blogNavigation from '@/app/contexts/BlogNavigation';
import Widget from '../../../../components/Widget';

export default function BlogViewClient({ 
  blogPost,
  language,
  langStrings,
  metaTitle,
  metaDescription,
  error,
  isLoading
}) {
  const [imageError, setImageError] = React.useState(false);
  const { languageStrings, updateLanguageStrings } = useLanguageStrings();
  const reviewedBy = reviewedByTranslations[language] || reviewedByTranslations.en;
  const blogNav = blogNavigation[language] || blogNavigation.en;

  // Cache language strings
  useEffect(() => {
    if (!languageStrings[language]) {
      updateLanguageStrings(language, langStrings);
    }
  }, [language, langStrings, languageStrings, updateLanguageStrings]);
  if (isLoading) {
    return (
      <div>
        <NavBar />
        <Box sx={{ 
          backgroundColor: '#F7F6F4', 
          py: 4,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '50vh'
        }}>
          <CircularProgress />
        </Box>
        <Footer />
      </div>
    );
  }

  if (error || !blogPost) {
    return (
      <div>
        <NavBar />
        <Box sx={{ backgroundColor: '#F7F6F4', py: 4 }}>
          <Container>
            <Typography variant="h4">
              {error || langStrings.noBlogsFound}
            </Typography>
          </Container>
        </Box>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Helmet>
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />
      
      {/* Header Section */}
      <Box sx={{ backgroundColor: '#F7F6F4', py: 6 }}>
        <Container maxWidth="lg">
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link href={getRedirectPath(`/${language}`)} passHref style={{ textDecoration: 'none' }}>
              <MuiLink color="inherit" underline="hover">
                {langStrings.home}
              </MuiLink>
            </Link>
            <Link href={getRedirectPath(`/${language}/blog`)} passHref style={{ textDecoration: 'none' }}>
              <MuiLink color="inherit" underline="hover">
                {blogNav}
              </MuiLink>
            </Link>
            <Typography color="text.primary">{blogPost.title}</Typography>
          </Breadcrumbs>

          {/* Title */}
          <Typography 
            variant="h2" 
            component="h1" 
            gutterBottom
            sx={{ 
              fontWeight: 'bold',
              mb: 2, 
              wordWrap: 'break-word',
            }}
          >
            {blogPost.title}
          </Typography>
          
          {/* Author and Date */}
          <Box sx={{ mb: 4 }}>
            {blogPost.author && (
              <Typography 
                variant="subtitle1" 
                sx={{ color: 'text.secondary', mb: 1 }}
              >
                {langStrings.by} {blogPost.author}
              </Typography>
            )}
            {blogPost.reviewer_name && (
              <Typography 
                variant="subtitle1" 
                sx={{ color: 'text.secondary', mb: 1 }}
              >
                {reviewedBy}{' '}
                <Link 
                  href={getRedirectPath(`/${language}/author/view/${blogPost.reviewer_slug}`)}
                  style={{ textDecoration: 'none' }}
                >
                  <MuiLink
                    component="span"
                    sx={{ 
                      color: 'text.secondary',
                      textDecoration: 'none',
                      '&:hover': {
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    {blogPost.reviewer_name}
                  </MuiLink>
                </Link>
              </Typography>
            )}
            {blogPost.published_at && (
              <>
                <Typography 
                  variant="subtitle2" 
                  sx={{ color: 'text.secondary', mb: 2 }}
                >
                  {langStrings.publishedOn} {new Date(blogPost.published_at).toLocaleDateString()}
                </Typography>
                <Box sx={{ mt: 1 }}>
                  <ShareButton copiedText={'Link copied to clipboard'} />
                </Box>
              </>
            )}
          </Box>
        </Container>
      </Box>

      {/* Featured Image */}
      {blogPost.image?.src && !imageError && (
        <Box sx={{ py: 4, backgroundColor: '#fff' }}>
          <Container maxWidth="lg">
            <Box
              component="img"
              src={blogPost.image.src}
              alt={blogPost.image.alt || blogPost.title}
              onError={() => setImageError(true)}
              sx={{
                width: '100%',
                maxWidth: '800px',
                height: 'auto',
                display: 'block',
                margin: '0 auto',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                objectFit: 'cover'
              }}
            />
          </Container>
        </Box>
      )}

      {/* Main Content */}
      <Box sx={{ py: 6 }}>
        <Container maxWidth="lg">
          <Box 
            sx={{ 
              maxWidth: '800px',
              margin: '0 auto',
              '& img': {
                maxWidth: '100%',
                height: 'auto',
                borderRadius: '4px',
                display: 'block',
                margin: '1rem auto'
              },
              '& table': {
                width: '100%',
                borderCollapse: 'collapse',
                marginBottom: '2rem',
                '& th, & td': {
                  border: '1px solid #ddd',
                  padding: '12px',
                  textAlign: 'left'
                },
                '& th': {
                  backgroundColor: '#f5f5f5',
                  fontWeight: 600
                }
              },
              '& p': {
                marginBottom: '1rem',
                lineHeight: 1.6,
                fontSize: '1.1rem'
              },
              '& h2': {
                marginTop: '2rem',
                marginBottom: '1rem',
                fontSize: '1.8rem',
                fontWeight: 600
              },
              '& h3, & h4': {
                marginTop: '1.5rem',
                marginBottom: '0.75rem',
                fontSize: '1.4rem',
                fontWeight: 600
              },
              '& ul, & ol': {
                marginBottom: '1rem',
                paddingLeft: '2rem',
                '& li': {
                  marginBottom: '0.5rem',
                  lineHeight: 1.6
                }
              }
            }}
            dangerouslySetInnerHTML={{ __html: blogPost.body_html }}
          ></Box>
          <Widget isAtBottom={true}  title="Want a 1:1 answer for your situation?"
  description="Ask your question privately on August, your 24/7 personal AI health assistant."
  />
        </Container>
      </Box>
      <Footer />
    </div>
  );
}
