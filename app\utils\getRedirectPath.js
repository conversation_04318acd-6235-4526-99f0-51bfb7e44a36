const logger = require('./logger');

export function getRedirectPath(path) {
  logger.info('Environment:', typeof window === 'undefined' ? 'Server' : 'Client')
  logger.info('Path in Redirect:', path)
  const isDevelopment = !(process.env.NODE_ENV === 'production');
  
  if (isDevelopment || path.startsWith('/library')) {
      return path;
  }
  
  // Split the path into segments
  const segments = path.split('/').filter(Boolean);
  if (segments.length >= 1) {
      // Reconstruct the path with 'library' after the language code
      return `/${segments[0]}/library/${segments.slice(1).join('/')}`;
  }
  
  return path;
}
