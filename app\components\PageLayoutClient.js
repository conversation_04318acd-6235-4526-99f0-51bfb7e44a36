'use client';

import { Helmet } from 'react-helmet-async';
import PageLayout from './PageLayout';

export default function PageLayoutClient({ 
  heroProps, 
  categoryData, 
  metaTitle, 
  metaDescription 
}) {
  return (
    <div>
      <Helmet>    
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <PageLayout
        heroProps={heroProps}
        categoryData={categoryData}
      />
    </div>
  );
}