import { getHomeMetaData } from '../api/languages/getMetaData';
import { getFeaturedBlogs } from '../api/blog/meta/getFeaturedBlogs';
import HomeClient from './HomeClient';
const logger = require('../utils/logger');

export default async function Home(props) {
  const params = await props.params;
  const language = params?.lang || 'en';
  
  try {
    // Fetch metadata server-side
    let metadata = null;
    try {
      metadata = await getHomeMetaData(language);
    } catch (error) {
      logger.error("Error fetching home metadata:", error);
    }

    // Fetch featured blogs
    const featuredBlogs = await getFeaturedBlogs(language, 4);

    const categories = [
      {
        key: 'medications',
        href: `/${language}/medications`,
      },
      {
        key: 'tests',
        href: `/${language}/tests-procedures`,
      },
      {
        key: 'diseases',
        href: `/${language}/diseases-conditions`,
      },
      {
        key: 'symptoms',
        href: `/${language}/symptoms`,
      }
    ];

    return (
      <HomeClient
        language={language}
        categories={categories}
        initialMetadata={metadata}
        featuredBlogs={featuredBlogs}
      />
    );
  } catch (error) {
    logger.error('Error in Home:', error);
    return (
      <HomeClient
        language={language}
        categories={[]}
        featuredBlogs={[]}
        error={error.message}
      />
    );
  }
}
