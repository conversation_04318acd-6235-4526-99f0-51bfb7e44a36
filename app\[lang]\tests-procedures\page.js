import PageLayoutClient from '../../components/PageLayoutClient';
import translationStrings from './language/translations';
import { getProcedureIndexMetaData } from '@/app/api/tests-procedures/meta/getMetaDataIndex';
const logger = require('../../utils/logger');

export default async function TestsPage({ params }) {
  const language = params?.lang || 'en';
  const langStrings = translationStrings[language] || translationStrings.en;
  let metaTitle = langStrings.title || '';
  let metaDescription = langStrings.title || '';

  try {
    const metadata = await getProcedureIndexMetaData(language); // Use the language
    if (metadata) {
      metaTitle = metadata.title || langStrings.title;
      metaDescription = metadata.description || langStrings.title;
    } else {
        logger.warn(`No home page SEO tags found in DB for language: ${language}`);
    }
  } catch (error) {
    logger.error("Error fetching home metadata:", error);
    // Default values already set above
  }

  const heroProps = {
    title: langStrings.title,
    description: langStrings.description, 
     searchPlaceholder: langStrings.searchPlaceholder,
      browseByLetterText: langStrings.browseByLetter,
      baseUrl: `/${language}/tests-procedures`, 
    tags:['health_library', 'procedures'], 
    indices: {
      health_library: 'Health_Library',
    }
  };

  const commonTestsData = {
    title: langStrings.commonTestsTitle,
    description:langStrings.commonTestsDescription,
    items: [
      {
        title: langStrings.completeBloodCount, 
        description:langStrings.completeBloodCountDescription ,
         href: `/${language}/tests-procedures/view/complete-blood-count-cbc`
      },
      {
        title: langStrings.endoscopy,
        description: langStrings.endoscopyDescription, 
        href: `/${language}/tests-procedures/view/upper-endoscopy`
        
      },
      {
        title: langStrings.mriScan,
         description: langStrings.mriScanDescription,
         href: `/${language}/tests-procedures/view/mri`
         
      },
      {
        title: langStrings.ctScan,
        description: langStrings.ctScanDescription,
        href: `/${language}/tests-procedures/view/ct-scan`
        
      },
      {
        title: langStrings.colonoscopy,
        description: langStrings.colonoscopyDescription,
        href: `/${language}/tests-procedures/view/colonoscopy`
        
      },
      {
        title: langStrings.echocardiogram,
        description:langStrings.echocardiogramDescription ,
        href: `/${language}/tests-procedures/view/echocardiogram`
        
      },
      {
        title: langStrings.stressTest,
        description: langStrings.stressTestDescription,
        href: `/${language}/tests-procedures/view/stress-test`
        
      },
      {
        title: langStrings.mammogram,
        description: langStrings.mammogramDescription,
        href: `/${language}/tests-procedures/view/mammogram`
      }
    ]
  };

  return (
    <PageLayoutClient
      heroProps={heroProps}
      categoryData={commonTestsData}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
    />
  );
} 