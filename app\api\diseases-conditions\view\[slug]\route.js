import { query } from '@/app/lib/db';
import sectionMappings from '../../sectionMappings';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

// Fields that should be rendered as markdown
const markdownFields = [
  'overview',
  'symptoms',
  'when_to_see_doctor',
  'causes',
  'risk_factors',
  'complications',
  'prevention',
  'diagnosis',
  'treatment',
  'self_care',
  'preparing_for_your_appointment'
];

export async function GET(request, { params }) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';

    const languageId = languageMap[lang] || 1;
    
     let result;
     if (languageId != 1){

      result = await query(
        `SELECT
            c.id,
            COALESCE(ct.name, c.name) AS name, 
            c.slug,
            c.overview AS c_overview,
            c.symptoms AS c_symptoms,
            c.when_to_see_doctor AS c_when_to_see_doctor,
            c.causes AS c_causes,
            c.risk_factors AS c_risk_factors,
            c.complications AS c_complications,
            c.prevention AS c_prevention,
            c.diagnosis AS c_diagnosis,
            c.treatment AS c_treatment,
            c.self_care AS c_self_care,
            c.preparing_for_your_appointment AS c_preparing_for_your_appointment,
            ct.overview AS overview,
            ct.symptoms AS symptoms,
            ct.when_to_see_doctor AS when_to_see_doctor,
            ct.causes AS causes,
            ct.risk_factors AS risk_factors,
            ct.complications AS complications,
            ct.prevention AS prevention,
            ct.diagnosis AS diagnosis,
            ct.treatment AS treatment,
            ct.self_care AS self_care,
            ct.preparing_for_your_appointment AS preparing_for_your_appointment,
            c.created_at,
            c.updated_at,
            cct.title AS meta_title, 
            cct.description AS meta_description
        FROM
            condition c
            LEFT JOIN condition_translations ct ON c.id = ct.condition_id AND ct.language_id = $2
            LEFT JOIN condition_meta_tags cct ON c.id = cct.condition_id AND cct.language_id = $2
        WHERE
            c.slug = $1
        `,
       [slug,languageId]
        );
 } else {
        result = await query(
        `SELECT 
            c.id,
            c.name,
            c.slug,
            c.overview,
            c.symptoms,
            c.when_to_see_doctor,
            c.causes,
            c.risk_factors,
            c.complications,
            c.prevention,
            c.diagnosis,
            c.treatment,
            c.self_care,
            c.preparing_for_your_appointment,
          c.created_at,
          c.updated_at, 
          cct.title AS meta_title, 
          cct.description AS meta_description
        FROM condition c
        LEFT JOIN condition_meta_tags cct ON c.id = cct.condition_id AND cct.language_code = 'en'
        WHERE c.slug = $1`,
          [slug]
        );
 }

    if (result.rows.length === 0) {
      return Response.json(
        { error: 'Condition not found' },
        { status: 404 }
      );
    }

    const condition = result.rows[0];

    // Transform the data to include sections for markdown content
    const sections = markdownFields
   .filter(field => {
      const translatedContent = condition[field]
      const englishContent = condition[`c_${field}`]
      return (translatedContent || englishContent) &&  (translatedContent !== 'NaN' && translatedContent !== null && translatedContent !== undefined ) || (englishContent !== 'NaN' && englishContent !== null && englishContent !== undefined)
   })
    .map(field => ({
      heading: sectionMappings[lang][field],
       content: condition[field] || condition[`c_${field}`]
      }));

    // Construct the response
    const response = {
      id: condition.id,
      name: condition.name,
      slug: condition.slug,
      url: condition.url,
      sections: sections,
      meta: {
        created_at: condition.created_at,
        updated_at: condition.updated_at
      }
    };
    return Response.json(response);
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load condition details' },
      { status: 500 }
    );
  }
} 