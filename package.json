{"name": "health-library-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@next/mdx": "^15.3.2", "@vercel/postgres": "^0.10.0", "algoliasearch": "^5.19.0", "lodash": "^4.17.21", "next": "^15.1.7", "pg": "^8.13.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-helmet-async": "^2.0.5", "react-markdown": "^9.0.1", "react-papaparse": "^4.4.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^15.0.0", "@types/node": "22.13.1", "@types/react": "19.0.8", "@types/react-dom": "^19.0.2", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.7.3", "webpack-bundle-analyzer": "^4.10.2"}}