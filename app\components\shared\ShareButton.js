'use client';

import React, { useState } from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import IosShareIcon from '@mui/icons-material/IosShare';

export default function ShareButton({ copiedText }) {
  const [showTooltip, setShowTooltip] = useState(false);

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 3000);
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Tooltip
        open={showTooltip}
        title={copiedText}
        placement="right"
        sx={{
          '& .MuiTooltip-tooltip': {
            backgroundColor: '#fff',
            color: 'text.primary',
            boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
            border: '1px solid #eee',
            padding: '8px 12px',
            fontSize: '0.875rem'
          }
        }}
      >
        <IconButton
          onClick={handleShare}
          size="small"
        >
          <IosShareIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  );
}
