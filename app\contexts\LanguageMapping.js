const languageMap = {
    'en': 1, // Assuming English is always ID 1
    'es': 2,
    'fr': 3,
    'de': 4,
    'it': 5,
    'pt': 6,
    'ru': 7,
    'zh-<PERSON>': 8,
    'zh-Hant': 9,
    'ja': 10,
    'ko': 11,
    'ar': 12,
    'hi': 13,
    'nl': 14,
    'pl': 15,
    'sv': 16,
    'no': 17,
    'da': 18,
    'fi': 19,
    'cs': 20,
    'hu': 21,
    'ro': 22,
    'el': 23,
    'uk': 24,
    'bg': 25,
    'hr': 26,
    'sk': 27,
    'sl': 28,
    'et': 29,
    'lv': 30,
    'lt': 31,
    'is': 32,
    'ga': 33,
    'mt': 34,
    'sq': 35,
    'be': 36,
    'bs': 37,
    'gd': 38,
    'lb': 39,
    'mk': 40,
    'sr': 41,
    'cy': 42,
    'vi': 43,
    'th': 44,
    'id': 45,
    'ms': 46,
    'tl': 47,
    'bn': 48,
    'ur': 49,
    'ta': 50,
    'te': 51,
    'mr': 52,
    'gu': 53,
    'kn': 54,
    'pa': 55,
    'ne': 56,
    'my': 57,
    'km': 58,
    'si': 59,
    'ml': 60,
    'mn': 61,
    'jv': 62,
    'su': 63,
    'sw': 64,
    'he': 65,
    'fa': 66,
    'tr': 67,
    'af': 68,
    'am': 69,
    'so': 70,
    'yo': 71,
    'zu': 72,
    'ha': 73,
    'ig': 74,
    'rw': 75,
    'om': 76,
    'sn': 77,
    'ht': 78,
    'mi': 79,
    'haw': 80,
    'la': 81
  };

  export default languageMap;