import { query } from '@/app/lib/db';
import sectionMappings from '../../sectionMappings';
import languageMap from '@/app/contexts/LanguageMapping';
import authors from '@/app/data/authors.json';
const logger = require('../../../../utils/logger');

export async function GET(request) {
  try {
    const slug = request.nextUrl.pathname.split('/').pop();
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';

    const languageId = languageMap[lang] || 1;

    // Get blog data from database
    const result = await query(
      `SELECT 
    bt.id, 
    CASE WHEN length(coalesce(bt.title, '')) = 0 THEN default_bt.title ELSE bt.title END as title, 
    bt.handle as slug, 
    bt.created_at, 
    bt.updated_at, 
    bt.published_at, 
    COALESCE(bt.author, default_bt.author) as author, 
    COALESCE(bt.body_html, default_bt.body_html) as body_html, 
    COALESCE(bt.tags, default_bt.tags) as tags, 
    COALESCE(bt.blog_title, default_bt.blog_title) as blog_title,
    COALESCE(bt.blog_id, default_bt.blog_id) as blog_id, 
    COALESCE(bt.user_id, default_bt.user_id) as user_id, 
    COALESCE(bt.summary_html, default_bt.summary_html) as summary_html, 
    COALESCE(bt.template_suffix, default_bt.template_suffix) as template_suffix,
    COALESCE(bt.admin_graphql_api_id, default_bt.admin_graphql_api_id) as admin_graphql_api_id, 
    COALESCE(bt.image, default_bt.image) as image, 
    COALESCE(bt.reviewer_slug, default_bt.reviewer_slug) as reviewer_slug
FROM 
    blogs_translations bt
LEFT JOIN 
    blogs_translations default_bt ON bt.id = default_bt.id AND default_bt.language_id = 1
WHERE 
    bt.handle = $1 
    AND bt.language_id = $2
LIMIT 1`, 
[slug,languageId]
    );

    if (result.rows.length === 0) {
      return Response.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    const blogPost = result.rows[0];

    // Construct the response using the database fields
    const response = {
      id: blogPost.id,
      title: blogPost.title,
      slug: blogPost.slug,
      created_at: blogPost.created_at,
      updated_at: blogPost.updated_at,
      published_at: blogPost.published_at,
      author: blogPost.author,
      body_html: blogPost.body_html,
      blog_id: blogPost.blog_id,
      user_id: blogPost.user_id,
      summary_html: blogPost.summary_html,
      template_suffix: blogPost.template_suffix,
      handle: blogPost.slug,
      tags: blogPost.tags,
      admin_graphql_api_id: blogPost.admin_graphql_api_id,
      blogTitle: blogPost.blog_title,
      image: blogPost.image,
      reviewer_slug: blogPost.reviewer_slug,
      reviewer_name: blogPost.reviewer_slug ? authors[blogPost.reviewer_slug]?.name : null
    };

    return Response.json(response);
  } catch (error) {
    logger.error('Error:', error);
    return Response.json(
      { error: 'Failed to load blog post details' },
      { status: 500 }
    );
  }
}
