'use client';

import { Suspense, lazy } from 'react';
import NavBar from '../components/NavBar';
// Optimized MUI imports for better tree shaking
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Link from 'next/link';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../contexts/LanguageContext';
import { getRedirectPath } from '@/app/utils/getRedirectPath';
import { useEffect } from 'react';
import { initPerformanceMonitoring, logBundleInfo } from '../utils/performance';

// Lazy load non-critical components
const SearchBar = lazy(() => import('../components/SearchBar'));
const Footer = lazy(() => import('../components/Footer'));

// Loading component for SearchBar
const SearchBarSkeleton = () => (
  <Box sx={{ 
    height: '56px', 
    backgroundColor: '#f5f5f5', 
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    px: 2
  }}>
    <Typography variant="body1" color="text.secondary">
      Loading search...
    </Typography>
  </Box>
);

// Loading component for Footer
const FooterSkeleton = () => (
  <Box sx={{ height: '200px', backgroundColor: '#f5f5f5', mt: 4 }} />
);

export default function HomeClient({
  language,
  categories,
  initialMetadata,
  featuredBlogs,
  error
}) {
  const { t } = useLanguage();
  const metaTitle = initialMetadata?.title || t('home.title') || '';
  const metaDescription = initialMetadata?.description || t('home.title') || '';

  // Initialize performance monitoring
  useEffect(() => {
    initPerformanceMonitoring();
    
    // Log bundle info after a short delay to ensure all resources are loaded
    const timer = setTimeout(() => {
      logBundleInfo();
    }, 2000);
    
    return () => clearTimeout(timer);
  }, []);

  if (error) {
    return (
      <div>
        <NavBar />
        <Box sx={{ backgroundColor: '#F7F6F4', py: 4 }}>
          <Container>
            <Typography variant="h4" color="error">
              {error}
            </Typography>
          </Container>
        </Box>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Helmet>    
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />

      {/* Hero Section */}
      <Box sx={{ backgroundColor: '#F7F6F4', py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h2"
                component="h1"
                gutterBottom
                sx={{ fontWeight: 'bold', mb: 3, wordWrap: 'break-word' }}
              >
                {t('home.title')}
              </Typography>
              <Typography
                variant="h6"
                sx={{ color: 'text.secondary', mb: 4 }}
              >
                {t('home.subtitle')}
              </Typography>
              <Suspense fallback={<SearchBarSkeleton />}>
                <SearchBar
                  placeholder={t('home.searchPlaceholder')}
                  indices={{ health_library: 'Health_Library' }}
                  tags={['health_library']}
                />
              </Suspense>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Categories Section */}
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h4"
            component="h2"
            gutterBottom
            sx={{ fontWeight: 'bold', mb: 4 }}
          >
            {t('home.commonCategories.title')}
          </Typography>

          <Grid container spacing={4}>
            {categories.map((category) => (
              <Grid item xs={12} sm={6} md={3} key={category.key}>
                <Link href={getRedirectPath(category.href)} style={{ textDecoration: 'none' }}>
                  <Card
                    sx={{
                      height: '100%',
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 3
                      },
                      borderRadius: '16px',
                      backgroundColor: '#F7F6F4',
                      padding: '24px 16px',
                      boxShadow: 'none',
                    }}
                  >
                    <CardContent>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ fontWeight: '500' }}
                      >
                        {t(`home.commonCategories.${category.key}.title`)}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                      >
                        {t(`home.commonCategories.${category.key}.description`)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Link>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
      {/* Featured Articles Section */}
      <Box sx={{ py: 8, marginBottom: '100px' }}>
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Typography
              variant="h4"
              component="h2"
              sx={{ fontWeight: 'bold' }}
            >
              {t('home.featuredArticles.title')}
            </Typography>
            <Link href={getRedirectPath(`/${language}/blog`)} style={{ textDecoration: 'none' }}>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'primary.main',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                {t('home.viewAll')}
              </Typography>
            </Link>
          </Box>

          <Grid container spacing={4}>
            {featuredBlogs.map((blog, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Link href={getRedirectPath(blog.href)} style={{ textDecoration: 'none' }}>
                  <Card
                    sx={{
                      height: '100%',
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 3
                      },
                      borderRadius: '16px',
                      backgroundColor: '#F7F6F4',
                      padding: '24px 16px',
                      boxShadow: 'none',
                    }}
                  >
                    <CardContent>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{
                          fontWeight: '500',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          lineHeight: 1.3,
                          mb: 2
                        }}
                      >
                        {blog.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {blog.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Link>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
      <Suspense fallback={<FooterSkeleton />}>
        <Footer />
      </Suspense>
    </div>
  );
}
