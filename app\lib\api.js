// Request deduplication cache
const requestCache = new Map();
const logger = require('../utils/logger');

async function fetchWithRetry(url, options, retries = 3, backoff = 300) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response;
    } catch (error) {
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, backoff * Math.pow(2, i)));
    }
  }
}

export async function fetchByLetter(category, letter, lang = 'en', page = 1, limit = 20) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const apiUrl = new URL(`/api/${category}/by-letter/${letter}`, baseUrl);
    apiUrl.searchParams.set('lang', lang);
    apiUrl.searchParams.set('page', page.toString());
    apiUrl.searchParams.set('limit', limit.toString());

    const cacheKey = apiUrl.toString();
    
    // Check for in-flight requests
    if (requestCache.has(cacheKey)) {
      return requestCache.get(cacheKey);
    }

    const starttime = Date.now();
    
    const fetchPromise = (async () => {
      try {
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Accept-Language': lang,
            'Content-Type': 'application/json',
          },
          next: {
            revalidate: 3600, // Cache for 1 hour
            tags: [`${category}-${letter}-${lang}`], // Cache tag for invalidation
          },
        });

        const data = await response.json();
        const endtime = Date.now();
        logger.info('Fetch took:', endtime - starttime, 'milliseconds');

        return {
          items: data.items || [],
          pagination: data.pagination || {
            total: 0,
            page: 1,
            limit: 20,
            totalPages: 0
          }
        };
      } finally {
        // Clean up cache entry
        requestCache.delete(cacheKey);
      }
    })();

    // Store the promise in the cache
    requestCache.set(cacheKey, fetchPromise);
    
    return fetchPromise;
  } catch (error) {
    logger.error('API Error:', error);
    throw new Error('Failed to fetch data. Please try again later.');
  }
}


export async function fetchBySlug(category, slug, lang = 'en') {
  try {
    logger.info('hello')
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const apiUrl = new URL(`/api/${category}/view/${slug}`, baseUrl);
    apiUrl.searchParams.set('lang', lang);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept-Language': lang,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    logger.error('API Error:', error);
    throw error;
  }
}
