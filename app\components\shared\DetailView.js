'use client';
import { Box, Container, Typography, CircularProgress } from '@mui/material';
import NavBar from '../NavBar';
import BreadcrumbNav from './BreadcrumbNav';
import HeroContent from './HeroContent';
import <PERSON>rrorView from './ErrorView';
import Footer from './../Footer';
import { Helmet } from 'react-helmet-async';
import Widget from '../Widget';

export default function DetailView({
  loading,
  error,
  data,
  breadcrumbItems,
  children,
  metaTitle,       
  metaDescription,
  langStrings,
}) {
  if (loading) {
    return (
      <div>
        <NavBar />
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      </div>
    );
  }

  if (error || !data) {
    return <ErrorView message={error} />;
  }

  return (
    <div>
      <Helmet>
        <title>{metaTitle}</title>
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={metaTitle} />
        <meta property="og:description" content={metaDescription} />
      </Helmet>
      <NavBar />
      <HeroContent
        title={data.title || data.name}
        description={
          data.description || data.short_description || data.overview
        }
        langStrings={langStrings || {}}
      >
        <BreadcrumbNav items={breadcrumbItems} />
      </HeroContent>
      <Box sx={{ pb: 6, pt: 3 }}>
        <Container maxWidth="lg">
          <Widget />
          <Box
            sx={{
              maxWidth: "800px",
              wordWrap: "break-word",
              overflowWrap: "break-word",
              "& *": {
                wordWrap: "break-word",
                overflowWrap: "break-word",
                hyphens: "auto",
              },
            }}
          >
            {children}
          </Box>
        </Container>
      </Box>
      <Widget
        isAtBottom={true}
        title="Want a 1:1 answer for your situation?"
        description="Ask your question privately on August, your 24/7 personal AI health assistant."
      />
      <Footer />
    </div>
  );
}
