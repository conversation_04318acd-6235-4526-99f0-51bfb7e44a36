'use client';

import { Box, Typography } from '@mui/material';
import Link from 'next/link';
import DetailView from './DetailView';
import ContentSection from '../ContentSection';
import Widget from '../Widget';
import { getRedirectPath } from '@/app/utils/getRedirectPath';

export default function DetailViewClient({ 
  condition, 
  error,
  breadcrumbItems,
  metaTitle,
  metaDescription,
  language,
  langStrings
}) {
  return (
    <DetailView
      loading={false}
      error={error}
      data={condition}
      breadcrumbItems={breadcrumbItems}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
      langStrings={langStrings}
    >
      {condition && (
        <>
          <ContentSection sections={condition?.sections || []} />
          {condition?.url && (
            <Box sx={{ mt: 4, pt: 4, borderTop: '1px solid', borderColor: 'divider' }}>
              <Typography variant="body2" color="text.secondary">
                {langStrings.originalSource}
                <Link href={getRedirectPath(condition.url)} target="_blank" rel="noopener noreferrer">
                  {condition.url}
                </Link>
              </Typography>
            </Box>
          )}
        </>
      )}
    </DetailView>
  );
}
