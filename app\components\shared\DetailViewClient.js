'use client';

import { Box, Typography, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Link from 'next/link';
import DetailView from './DetailView';
import ContentSection from '../ContentSection';
import Widget from '../Widget';
import { getRedirectPath } from '@/app/utils/getRedirectPath';

function stripBodyTag(html) {
  if (!html) return "";
  const match = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
  return match ? match[1] : html;
}

function extractFirstH1(html) {
  if (!html) return null;
  const container = document.createElement("div");
  container.innerHTML = html;
  const h1 = container.querySelector("h1");
  return h1 ? h1.textContent.trim() : null;
}


// Helper to parse and split FAQ section
function extractFAQandRest(html) {
  if (!html) return { restHtml: '', faq: [] };
  // Create a DOM tree
  const container = document.createElement('div');
  container.innerHTML = html;

  let faqStartIdx = -1;
  let faqEndIdx = -1;
  let nodes = Array.from(container.childNodes);
  // Find the FAQ heading
  for (let i = 0; i < nodes.length; i++) {
    if (
      nodes[i].nodeType === 1 &&
      /^h[1-6]$/i.test(nodes[i].nodeName) &&
      nodes[i].textContent.trim().toLowerCase().startsWith('frequently asked questions')
    ) {
      faqStartIdx = i;
      // Find where FAQ ends (next h2/h1 or end)
      for (let j = i + 1; j < nodes.length; j++) {
        if (
          nodes[j].nodeType === 1 &&
          /^h[12]$/i.test(nodes[j].nodeName)
        ) {
          faqEndIdx = j;
          break;
        }
      }
      if (faqEndIdx === -1) faqEndIdx = nodes.length;
      break;
    }
  }
  // If no FAQ section, return all as rest
  if (faqStartIdx === -1) {
    return { restHtml: html, faq: [] };
  }
  // Extract FAQ nodes
  const faqNodes = nodes.slice(faqStartIdx, faqEndIdx);
  // Remove FAQ nodes from main
  const restNodes = nodes.slice(0, faqStartIdx).concat(nodes.slice(faqEndIdx));
  // Build restHtml
  const restHtml = restNodes.map(n => n.outerHTML || n.textContent).join('');

  // Parse FAQ into Q/A pairs
  let faq = [];
  let currentQ = null;
  for (let i = 0; i < faqNodes.length; i++) {
    const node = faqNodes[i];
    if (
      node.nodeType === 1 &&
      /^h[3-6]$/i.test(node.nodeName)
    ) {
      // New question
      if (currentQ) faq.push(currentQ);
      currentQ = { question: node.textContent, answerNodes: [] };
    } else if (currentQ && node.nodeType === 1) {
      currentQ.answerNodes.push(node);
    }
  }
  if (currentQ) faq.push(currentQ);

  // Convert answerNodes to HTML
  faq = faq.map(q => ({
    question: q.question,
    answerHtml: q.answerNodes.map(n => n.outerHTML || n.textContent).join('')
  }));

  return { restHtml, faq };
}


export default function DetailViewClient({ 
  condition, 
  error,
  breadcrumbItems,
  metaTitle,
  metaDescription,
  language,
  langStrings
}) {
  let firstHeading = "";
  let restHtml = '';
  let faq = [];
  function removeFirstH1(html) {
    if (!html) return html;
    return html.replace(/<h1[^>]*>.*?<\/h1>/i, "");
  }
  if (condition && condition.sections) {
    const { restHtml: r, faq: f } = extractFAQandRest(stripBodyTag(condition.sections));
    restHtml = removeFirstH1(r);
    faq = f;
    firstHeading = extractFirstH1(stripBodyTag(condition.sections));
  }
  return (
    <DetailView
      loading={false}
      error={error}
      firstHeading={firstHeading}
      data={condition}
      breadcrumbItems={breadcrumbItems}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
      langStrings={langStrings}
    >
      {condition && (
        <>
          <Box
            sx={{
              "& img": {
                maxWidth: "100%",
                height: "auto",
                borderRadius: "4px",
                display: "block",
                margin: "1rem auto",
              },
              "& table": {
                width: "100%",
                borderCollapse: "collapse",
                marginBottom: "2rem",
                "& th, & td": {
                  border: "1px solid #ddd",
                  padding: "12px",
                  textAlign: "left",
                },
                "& th": {
                  backgroundColor: "#f5f5f5",
                  fontWeight: 600,
                },
              },
              "& p": {
                marginBottom: "1rem",
                lineHeight: 1.6,
                fontSize: "1.1rem",
              },
              "& h1": {
                marginTop: "2rem",
                marginBottom: "1rem",
                fontSize: "2.2rem",
                fontWeight: 700,
              },
              "& h2": {
                marginTop: "2rem",
                marginBottom: "1rem",
                fontSize: "1.8rem",
                fontWeight: 600,
              },
              "& h3": {
                marginTop: "1.5rem",
                marginBottom: "0.75rem",
                fontSize: "1.5rem",
                fontWeight: 600,
              },
              "& h4": {
                marginTop: "1.2rem",
                marginBottom: "0.75rem",
                fontSize: "1.3rem",
                fontWeight: 600,
              },
              "& ul, & ol": {
                marginBottom: "1rem",
                paddingLeft: "2rem",
                listStyle: "disc",
                "& li": {
                  marginBottom: "0.5rem",
                  lineHeight: 1.6,
                },
              },
              "& a": {
                color: "#1976d2",
                textDecoration: "underline",
                "&:hover": {
                  textDecoration: "none",
                },
              },
              "& blockquote": {
                borderLeft: "4px solid #ccc",
                paddingLeft: "1rem",
                color: "#555",
                fontStyle: "italic",
                margin: "1rem 0",
              },
              "& code": {
                backgroundColor: "#f4f4f4",
                padding: "0.2em 0.4em",
                borderRadius: "4px",
                fontFamily: "monospace",
                fontSize: "0.95em",
              },
              "& pre": {
                backgroundColor: "#f4f4f4",
                padding: "1rem",
                borderRadius: "4px",
                overflowX: "auto",
                fontFamily: "monospace",
                fontSize: "0.95em",
                marginBottom: "1.5rem",
              },
              "& strong": {
                fontWeight: 700,
              },
              "& em": {
                fontStyle: "italic",
              },
              "& hr": {
                border: 0,
                borderTop: "1px solid #ccc",
                margin: "2rem 0",
              },
              "& figure": {
                margin: "1rem auto",
                textAlign: "center",
              },
              "& figcaption": {
                fontSize: "0.9rem",
                color: "#666",
                marginTop: "0.5rem",
              },
              "& iframe": {
                maxWidth: "100%",
                display: "block",
                margin: "1.5rem auto",
              },
            }}
          >
            {/* Render the rest of the HTML */}
            <div dangerouslySetInnerHTML={{ __html: restHtml }} />
            {/* Render FAQ accordion if present */}
            {faq.length > 0 && (
              <Box mt={4}>
                <Typography variant="h2" mb={2} fontWeight={600}>
                  Frequently asked questions about{" "}
                  {condition.title || condition.name || "this"}
                </Typography>
                {faq.map((item, idx) => (
                  <Accordion
                    key={idx}
                    sx={{
                      mb: 1,
                      borderRadius: 1,
                      boxShadow: "none",
                      border: "1px solid #e0e0e0",
                      overflow: "hidden",
                      "& .MuiTypography-root": {
                        margin: 1, // Remove default margin
                        lineHeight: 1.3, // Tighter line height
                      },
                    }}
                  >
                    <AccordionSummary mb={0} expandIcon={<ExpandMoreIcon />}>
                      <Typography mb={0} fontWeight={600}>
                        <Box
                          component="span"
                          color="primary.main"
                          fontWeight={700}
                          mr={1}
                          mb={0}
                        >
                          Q{idx + 1}:
                        </Box>
                        {item.question}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box
                        sx={{
                          background: "#f9f9fc",
                          borderRadius: 2,
                          p: 2,
                          border: "1px solid #e3e6f0",
                          boxShadow: "0 1px 2px rgba(0,0,0,0.03)",
                        }}
                      >
                        <div
                          style={{ lineHeight: 1.7, fontSize: "1.08rem" }}
                          dangerouslySetInnerHTML={{ __html: item.answerHtml }}
                        />
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </Box>
            )}
          </Box>
        </>
      )}
    </DetailView>
  );
}
