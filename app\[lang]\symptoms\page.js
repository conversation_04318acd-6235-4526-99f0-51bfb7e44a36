import PageLayoutClient from '../../components/PageLayoutClient';
import translationStrings from './language/translations'; // Import translations
import { getSymptomsIndexMetaData } from '@/app/api/symptoms/meta/getMetaDataIndex';
const logger = require('../../utils/logger');

export default async function SymptomsPage({ params }) {
  const language = params?.lang || 'en';
  const langStrings = translationStrings[language] || translationStrings.en;
  
  let metaTitle = langStrings.symptomsTitle || '';
  let metaDescription = langStrings.symptomsTitle || '';

  try {
    const metadata = await getSymptomsIndexMetaData(language);
    if (metadata) {
      metaTitle = metadata.title || langStrings.title;
      metaDescription = metadata.description || langStrings.title;
    } else {
      logger.warn(`No home page SEO tags found in DB for language: ${language}`);
    }
  } catch (error) {
    logger.error("Error fetching home metadata:", error);
    // Default values already set above
  }

  const heroProps = {
    title: langStrings.title,
    description: langStrings.description,
    searchPlaceholder: langStrings.searchPlaceholder,
    browseByLetterText: langStrings.browseByLetter,
    baseUrl: `/${language}/symptoms`, 
    tags:['health_library', 'symptoms'], 
    indices: {
      health_library: 'Health_Library',
    }
  };

  const commonSymptomsData = {
    title: langStrings.commonSymptomsTitle,
    description: langStrings.commonSymptomsDescription,
    items: [
      {
        title: langStrings.cough,
        description: langStrings.coughDescription,
        href: `/${language}/symptoms/view/cough`
      },
      {
        title: langStrings.headache,
        description: langStrings.headacheDescription,
        href: `/${language}/symptoms/view/headache`
      },
      {
        title: langStrings.fatigue,
        description: langStrings.fatigueDescription,
        href: `/${language}/symptoms/view/fatigue`
      },
      {
        title: langStrings.abdominalPain,
        description: langStrings.abdominalPainDescription,
        href: `/${language}/symptoms/view/abdominal-pain`
      },
      {
         title: langStrings.dizziness,
         description: langStrings.dizzinessDescription,
        href: `/${language}/symptoms/view/dizziness`
      },
      {
        title: langStrings.backPain,
       description: langStrings.backPainDescription,
        href: `/${language}/symptoms/view/back-pain`
      },
      {
        title: langStrings.nausea,
       description: langStrings.nauseaDescription,
        href: `/${language}/symptoms/view/nausea-and-vomiting`
      },
      {
        title: langStrings.shortnessOfBreath,
        description: langStrings.shortnessOfBreathDescription,
        href: `/${language}/symptoms/view/shortness-of-breath`
      }
    ]
  };

  return (
    <PageLayoutClient
      heroProps={heroProps}
      categoryData={commonSymptomsData}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
    />
  );
}