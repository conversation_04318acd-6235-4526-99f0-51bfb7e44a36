import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
const logger = require('../../utils/logger');

const SearchResultPage = () => {
    const router = useRouter();
    const { id } = router.query; // Get the ID from the URL
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (id) {
            // Fetch the data based on the ID and category
            const fetchData = async () => {
                const response = await fetch(`https://8USGLTP3J9-dsn.algolia.net/1/indexes/${id}/objectID`, {
                    method: 'GET',
                    headers: {
                        'X-Algolia-API-Key': '********************************',
                        'X-Algolia-Application-Id': '8USGLTP3J9',
                    },
                });

                if (response.ok) {
                    const result = await response.json();
                    setData(result);
                } else {
                    logger.error('Failed to fetch data');
                }
                setLoading(false);
            };

            fetchData();
        }
    }, [id]);

    if (loading) return <div>Loading...</div>;
    if (!data) return <div>No data found</div>;

    return (
        <div>
            <h1>{data.name}</h1>
            <p>{data.description || data.overview}</p>
            {/* Render other fields based on the type of data */}
            {/* Example: */}
            {data.brand_name && <p>Brand: {data.brand_name}</p>}
            {data.side_effects && <p>Side Effects: {data.side_effects}</p>}
            {/* Add more fields as necessary */}
        </div>
    );
};

export default SearchResultPage; 