import { query } from '@/app/lib/db';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

export const revalidate = 3600;


export async function GET(request, { params }) {

  const cacheControl = request.headers.get('cache-control') || '';
  if (!cacheControl.includes('no-cache')) {
    // Cache the response for 1 hour on the edge
    request.headers.set('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate=86400');
  }

  try {
    const { letter } = await params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const offset = (page - 1) * limit;   
    
    const languageId = languageMap[lang] || 1;

    let result;
    if (languageId != 1) {
      // Get total count
      result = await query(
        `WITH data AS (
          SELECT  s.id,
          COALESCE(st.name, s.name) AS name,
          s.definition,
          s.slug,
          pst.title AS page_title,
          pst.description AS page_description,
          pst.indextitle AS page_indextitle,
          COUNT(*) OVER() AS total_count
          FROM symptom s
          LEFT JOIN symptom_translations st ON s.id = st.symptom_id AND st.language_id = $2
          LEFT JOIN symptoms_page_meta_tags pst ON st.language_id = pst.language_id
          WHERE st.first_letter = UPPER($1)
          ORDER BY COALESCE(st.name, s.name) ASC
          LIMIT $3 OFFSET $4
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, languageId, limit, offset]

      );
    } else {
      result = await query(
        `WITH data AS (
          SELECT s.id,
          s.name,
          s.definition,
          s.slug,
          pst.title AS page_title,
          pst.description AS page_description,
          pst.indextitle AS page_indextitle,
          COUNT(*) OVER() AS total_count
          FROM symptom s
          LEFT JOIN symptoms_page_meta_tags pst ON pst.language_id = 1
          WHERE s.first_letter = UPPER($1)
          ORDER BY s.name ASC
          LIMIT $2 OFFSET $3
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, limit, offset]
      );
    }

    const totalCount = result.rows[0]?.total || 0;
    return Response.json({ 
      items: result.rows,
      pagination: {
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit)
      }
  });
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load symptoms' },
      { status: 500 }
    );
  }
} 