import { query } from '@/app/lib/db';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

export const revalidate = 3600; // Cache for 1 hour

export async function GET(request, { params }) {
  // Enable edge caching
  const cacheControl = request.headers.get('cache-control') || '';
  if (!cacheControl.includes('no-cache')) {
    // Cache the response for 1 hour on the edge
    request.headers.set('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate=86400');
  }

  try {
    
    const { letter } = await params;
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'en';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    const offset = (page - 1) * limit;

    const languageId = languageMap[lang] || 1;
    
    let result;

    if (languageId != 1) {
      result = await query(
        `WITH data AS (
          SELECT
            m.id,
            COALESCE(mt.name, m.name) AS name,
            m.slug,
            pmt.title AS page_title,
            pmt.description AS page_description,
            pmt.indextitle AS page_indextitle,
            COUNT(*) OVER() AS total_count
          FROM
            medications m
          LEFT JOIN medications_translations mt ON m.id = mt.medications_id AND mt.language_id = $2
          LEFT JOIN medications_page_meta_tags pmt ON mt.language_id = pmt.language_id
          WHERE
            mt.first_letter = UPPER($1)
          ORDER BY
            COALESCE(mt.name, m.name) ASC
          LIMIT $3 OFFSET $4
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, languageId, limit, offset]
      );
    } else {
      result = await query(
        `WITH data AS (
          SELECT
            m.id,
            m.name,
            m.slug,
            pmt.title AS page_title,
            pmt.description AS page_description,
            pmt.indextitle AS page_indextitle,
            COUNT(*) OVER() AS total_count
          FROM
            medications m
          LEFT JOIN medications_page_meta_tags pmt ON pmt.language_id = 1
          WHERE
            m.first_letter = UPPER($1)
          ORDER BY
            m.name ASC
          LIMIT $2 OFFSET $3
        )
        SELECT *, (SELECT COALESCE(MAX(total_count), 0) FROM data) as total FROM data`,
        [letter, limit, offset]
      );
    }

    const totalCount = result.rows[0]?.total || 0;


    return new Response(JSON.stringify({
      items: result.rows,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400'
      }
    });

  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load medications' },
      { status: 500 }
    );
  }
}