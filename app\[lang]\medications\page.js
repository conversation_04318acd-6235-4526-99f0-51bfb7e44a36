import PageLayoutClient from '../../components/PageLayoutClient';
import translationStrings from './language/translations';
import { getMedicationsIndexMetaData } from '@/app/api/medications/meta/getMetaDataIndex';
const logger = require('../../utils/logger');

export default async function MedicationsPage({ params }) {
  const language = params?.lang || 'en';
  const langStrings = translationStrings[language] || translationStrings.en;

  // Fetch metadata server-side
  let metaTitle = langStrings.medicationsTitle || '';
  let metaDescription = langStrings.medicationsTitle || '';

  try {
    const metadata = await getMedicationsIndexMetaData(language);
    if (metadata) {
      metaTitle = metadata.title || langStrings.medicationsTitle;
      metaDescription = metadata.description || langStrings.medicationsTitle;
    } else {
      logger.warn(`No home page SEO tags found in DB for language: ${language}`);
    }
  } catch (error) {
    logger.error("Error fetching home metadata:", error);
    // Default values already set above
  }

  const heroProps = {
    title: langStrings.medicationsTitle,
    description: langStrings.medicationsDescription,
    searchPlaceholder: langStrings.medicationsSearchPlaceholder,
    browseByLetterText: langStrings.browseByLetter,
    baseUrl: `/${language}/medications`,
    indices: {
      health_library: 'Health_Library',
    },
    tags: ['medications', 'health_library']
  };

  const commonMedicationsData = {
    title: langStrings.commonMedicationsTitle,
    description: langStrings.commonMedicationsDescription,
    items: [
      {
        title: langStrings.lisinoprilTitle,
        description: langStrings.lisinoprilDescription,
        href: `/${language}/medications/view/lisinopril-oral-route`
      },
      {
        title: langStrings.metforminTitle,
        description: langStrings.metforminDescription,
        href: `/${language}/medications/view/metformin-oral-route`
      },
      {
        title: langStrings.vitaminDTitle,
        description: langStrings.vitaminDDescription,
        href: `/${language}/medications/view/vitamin-d-and-related-compounds-oral-route-parenteral-route`
      },
      {
        title: langStrings.omeprazoleTitle,
        description: langStrings.omeprazoleDescription,
        href: `/${language}/medications/view/omeprazole-oral-route`
      },
      {
        title: langStrings.fishOilTitle,
        description: langStrings.fishOilDescription,
        href: `/${language}/medications/view/fat-emulsion-fish-oil-and-soybean-oil-intravenous-route`
      },
      {
        title: langStrings.atorvastatinTitle,
        description: langStrings.atorvastatinDescription,
        href: `/${language}/medications/view/atorvastatin-oral-route`
      },
      {
        title: langStrings.aspirinTitle,
        description: langStrings.aspirinDescription,
        href: `/${language}/medications/view/aspirin-oral-route`
      },
      {
        title: langStrings.sertralineTitle,
        description: langStrings.sertralineDescription,
        href: `/${language}/medications/view/sertraline-oral-route`
      }
    ]
  };

  return (
    <PageLayoutClient
      heroProps={heroProps}
      categoryData={commonMedicationsData}
      metaTitle={metaTitle}
      metaDescription={metaDescription}
    />
  );
}
