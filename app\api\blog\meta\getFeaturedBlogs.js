import { query } from '@/app/lib/db';
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../utils/logger');

export async function getFeaturedBlogs(language, limit = 8) {
  try {

    const languageId = languageMap[language] || 1;

    const result = await query(
      `SELECT 
    bt.id, 
    CASE WHEN length(coalesce(bt.title, '')) = 0 THEN default_bt.title ELSE bt.title END as title, 
    bt.handle as slug, 
    COALESCE(bt.body_html, default_bt.body_html) as body_html, 
    bt.published_at, 
    COALESCE(bt.blog_title, default_bt.blog_title) as "blogTitle"
FROM 
    blogs_translations bt
LEFT JOIN 
    blogs_translations default_bt ON bt.id = default_bt.id AND default_bt.language_id = 1
WHERE 
    bt.language_id = $1
ORDER BY 
    bt.published_at DESC 
LIMIT $2`,
      [languageId, limit]
    );

    return result.rows.map(blog => ({
      title: blog.title,
      description: blog.body_html.replace(/<[^>]*>/g, '').substring(0, 150) + '...', // Strip HTML and limit to 150 chars
      href: `/${language}/blog/view/${blog.slug}`
    }));
  } catch (error) {
    logger.error('Error fetching featured blogs:', error);
    return [];
  }
}
