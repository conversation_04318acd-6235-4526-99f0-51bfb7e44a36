if (!process.env.POSTGRES_HOST) {
  throw new Error('Please define the POSTGRES_HOST environment variable inside .env.local');
}

const config = {
  host: process.env.POSTGRES_HOST,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DB,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  ssl: {
    rejectUnauthorized: false // You might want to adjust this based on your SSL setup
  }
};

export default config; 