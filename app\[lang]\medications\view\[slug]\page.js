//page.js
import translationStrings from '../../language/translations';
import MedicationViewClient from './MedicationViewClient';
const logger = require('../../../../utils/logger');

async function fetchMedication(slug, lang) {
    try {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
        const startTime = Date.now(); // Start timer before fetch
        const response = await fetch(`${baseUrl}/api/medications/view/${slug}?lang=${lang}`, {
            method: 'GET',
            headers: {
                'Accept-Language': lang,
                'Content-Type': 'application/json',
            },
            cache: 'no-store'
        });
        const endTime = Date.now(); // End timer after fetch
        logger.info(`fetchMedication took ${endTime - startTime}ms`);

        if (!response.ok) {
            throw new Error('Failed to fetch medication');
        }
        return response.json();
    } catch (error) {
        logger.error('Error fetching medication:', error);
        throw error;
    }
}

export default async function MedicationPage({ params }) {
    const { slug, lang } = params;
    const language = lang || 'en';
    const startTime = Date.now();

    // Create a safe version of langStrings without functions
    const rawStrings = translationStrings[language] || translationStrings.en;
    const langStrings = {
        ...rawStrings,
        noMedicationsFound: undefined
    };

    try {
        // Fetch medication data, which now includes meta_title and meta_description
        const medication = await fetchMedication(slug, language);
        const { name, meta_title, meta_description } = medication;

        // Use the fetched meta data, or fall back to medication name if meta data is missing
        const metaTitle = meta_title || name || 'Default Title';
        const metaDescription = meta_description || name || 'Default Description';


        const renderClientComponentStart = Date.now();
        const component = (
            <MedicationViewClient
                medication={medication}
                language={language}
                langStrings={langStrings}
                metaTitle={metaTitle}
                metaDescription={metaDescription}
            />
        );
        const renderClientComponentEnd = Date.now();
        logger.info(`Rendering MedicationViewClient in MedicationPage took ${renderClientComponentEnd - renderClientComponentStart}ms`);

        return component;
    } catch (error) {
        logger.error('Error in MedicationPage:', error);
        return (
            <MedicationViewClient
                error={error.message}
                language={language}
                langStrings={langStrings}
            />
        );
    } finally {
        const endTime = Date.now();
        logger.info(`MedicationPage took ${endTime - startTime}ms`);
    }
}