import { query } from '@/app/lib/db';
import sectionMappings from './../../sectionMappings'; // Adjust path if necessary
import languageMap from '@/app/contexts/LanguageMapping';
const logger = require('../../../../utils/logger');

// Fields that should be rendered as markdown
const markdownFields = [
  'definition',
  'causes',
  'when_to_see_doctor'
];

export async function GET(request, { params }) {
  try {
      const { slug } = params;
      const { searchParams } = new URL(request.url);
      const lang = searchParams.get('lang') || 'en';
      const languageId = languageMap[lang] || 1;
    
      let result;

      if(languageId){
          result = await query(
              `SELECT
                    s.id,
                    COALESCE(st.name, s.name) AS name,
                    s.slug,
                    s.url as s_url,
                     s.definition as s_definition,
                   s.causes as s_causes,
                  s.when_to_see_doctor as s_when_to_see_doctor,
                    st.definition AS definition,
                    st.causes AS causes,
                    st.when_to_see_doctor AS when_to_see_doctor,
                    pst.title AS meta_title, 
                    pst.description AS meta_description,
                s.created_at
               FROM
                    symptom s
                LEFT JOIN
                    symptom_translations st ON s.id = st.symptom_id AND st.language_id = $2
                LEFT JOIN 
                    symptom_meta_tags pst ON s.id = pst.symptom_id AND pst.language_id = $2
                WHERE
                    s.slug = $1
                `,[slug, languageId]
          )
      } else {
           result = await query(
             `SELECT
                s.id,
                s.name,
                s.slug,
                 s.url,
                 s.definition,
                s.causes,
                s.when_to_see_doctor,
                s.created_at, 
                pst.title AS meta_title, 
                pst.description AS meta_description
            FROM
                symptom s
            LEFT JOIN symptom_meta_tags pst ON s.id = pst.symptom_id AND pst.language_code = 'en'
            WHERE
                s.slug = $1
              `, [slug]
            );
      }


    if (result.rows.length === 0) {
      return Response.json(
        { error: 'Symptom not found' },
        { status: 404 }
      );
    }

    const symptom = result.rows[0];
    // Transform the data to include sections for markdown content
   const sections = markdownFields
    .filter(field => {
      const translatedContent = symptom[field]
      const englishContent = symptom[`s_${field}`]
      return (translatedContent || englishContent) && (translatedContent !== 'NaN' && translatedContent !== null && translatedContent !== undefined) || (englishContent !== 'NaN' && englishContent !== null && englishContent !== undefined)
    })
    .map(field => ({
      heading: sectionMappings[lang][field],
       content: symptom[field] || symptom[`s_${field}`]
    }));

    // Construct the response
    const response = {
      id: symptom.id,
      name: symptom.name,
      slug: symptom.slug,
        url: symptom.s_url,
      sections: sections,
      meta: {
        created_at: symptom.created_at
      }
    };

    return Response.json(response);
  } catch (error) {
    logger.error('Database Error:', error);
    return Response.json(
      { error: 'Failed to load symptom details' },
      { status: 500 }
    );
  }
}