const fs = require('fs');
const path = require('path');

// Configuration
const languages = [
  'en', 'fr', 'de', 'es', 'it', 'sl', 'sk', 'mr', 'mk', 'bg', 'fi', 'he', 'el', 'ko', 'sr', 'lv', 'hr', 'ru', 'ja', 'tl', 'pt'
];
const baseUrl = 'https://www.meetaugust.ai';

// Language switcher URLs (circular navigation)
const languageSwitcherUrls = {
  'en': '/fr/calculators/',
  'fr': '/de/calculators/',
  'de': '/en/calculators/'
};

// Function to replace placeholders in template
function replacePlaceholders(template, locale) {
  let output = template;
  
  // Replace all placeholders with their values
  Object.entries(locale).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    output = output.replace(regex, value);
  });
  
  return output;
}

// Function to copy file
function copyFile(source, destination) {
  try {
    fs.copyFileSync(source, destination);
    console.log(`✓ Copied ${source} to ${destination}`);
  } catch (error) {
    console.error(`✗ Error copying ${source}:`, error.message);
  }
}

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✓ Created directory: ${dirPath}`);
  }
}

// Main build function
function buildCalculators() {
  console.log('🚀 Starting calculator build process...\n');
  
  try {
    // Read templates
    const calorieTemplate = fs.readFileSync('calculators/calorie-calculator-template.html', 'utf-8');
    
    console.log('✓ Templates loaded successfully');
    
    // Process each language
    languages.forEach(lang => {
      console.log(`\n📝 Processing ${lang.toUpperCase()}...`);
      
      // Read locale file
      const localePath = `calculators/locales/${lang}.json`;
      const locale = JSON.parse(fs.readFileSync(localePath, 'utf-8'));
      
      // Update language switcher URL
      locale.language_switcher_url = languageSwitcherUrls[lang];
      
      // Create output directories
      const baseOutputDir = `august-web-calculators/${lang}/calculators`;
      const calorieOutputDir = `${baseOutputDir}/calorie-calculator`;
      const assetsDest = `${baseOutputDir}/assets`;
      ensureDirectoryExists(calorieOutputDir);
      ensureDirectoryExists(assetsDest);
      
      // Generate calorie calculator page
      const calorieOutput = replacePlaceholders(calorieTemplate, locale);
      fs.writeFileSync(`${calorieOutputDir}/index.html`, calorieOutput);
      console.log(`✓ Generated ${calorieOutputDir}/index.html`);

      // Copy CSS and JS files for calorie calculator
      copyFile('calculators/calorie-calculator/style.css', `${calorieOutputDir}/style.css`);
      copyFile('calculators/calorie-calculator/index.js', `${calorieOutputDir}/index.js`);

      // Copy assets directory if it exists (calorie)
      const assetsSource = 'calculators/assets';
      if (fs.existsSync(assetsSource)) {
        // Copy all files in assets directory
        const assetFiles = fs.readdirSync(assetsSource);
        assetFiles.forEach(file => {
          copyFile(`${assetsSource}/${file}`, `${assetsDest}/${file}`);
        });
      }
    });
    
    console.log('\n🎉 Build completed successfully!');
    console.log('\n📁 Generated structure:');
    languages.forEach(lang => {
      console.log(`  /${lang}/calculators/calorie-calculator/`);
      console.log(`  /${lang}/calculators/calorie-calculator/index.html`);
      console.log(`  /${lang}/calculators/calorie-calculator/style.css`);
      console.log(`  /${lang}/calculators/calorie-calculator/index.js`);
    });
    
    console.log('\n🌐 Language switcher navigation:');
    console.log('  EN → FR → DE → EN (circular)');
    
  } catch (error) {
    console.error('\n❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Run the build
buildCalculators(); 