import { NextResponse } from "next/server";
import { languages, defaultLanguage } from "./app/lib/i18n/config";

export function middleware(request) {
  const { pathname, searchParams } = new URL(request.url);
  const hostname = request.headers.get("host") || "";

  // Skip all API routes in development
  if (process.env.NODE_ENV === "development" && pathname.startsWith("/api")) {
    return NextResponse.next();
  }
  const isLibrarySubdomain = hostname.startsWith('library.meetaugust.ai');
  
  if (isLibrarySubdomain) {
    // Allow API routes and static assets to be accessible from library subdomain
    if (
      pathname.startsWith('/api/') ||
      pathname.startsWith('/_next/') ||
      pathname.startsWith('/favicon.ico') ||
      pathname.startsWith('/robots.txt') ||
      pathname.startsWith('/sitemap') ||
      pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/)
    ) {
      return NextResponse.next();
    }
    
    // For all other routes (frontend pages), redirect to main domain
    const redirectUrl = new URL(`https://meetaugust.ai/en${pathname}`);
    
    // Preserve query parameters
    searchParams.forEach((value, key) => {
      redirectUrl.searchParams.set(key, value);
    });
    
    return NextResponse.redirect(redirectUrl, 301);
  }
  
  // Original i18n logic for main domain
  // Check if the pathname starts with /library
  const isLibraryPath = pathname.startsWith('/library');
  
  // Extract the path after /library if it exists, or use the full pathname
  const pathWithoutLibrary = isLibraryPath ? pathname.replace('/library', '') : pathname;
  
  // Check if the remaining path has a language prefix
  const pathnameHasLang = Object.keys(languages).some(
    (lang) => pathWithoutLibrary.startsWith(`/${lang}/`) || pathWithoutLibrary === `/${lang}`
  );
  
  if (!pathnameHasLang && pathWithoutLibrary!=='/api/languages' && pathWithoutLibrary!=='/august_logo_green.svg') {
    // Construct the new path based on whether it's a library path or not
    const newPath = isLibraryPath
      ? `/library/${defaultLanguage}${pathWithoutLibrary}`
      : `/${defaultLanguage}${pathWithoutLibrary}`;
    
    return NextResponse.redirect(
      new URL(newPath, request.url)
    );
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Skip all internal paths (_next) but include API routes for conditional handling
    '/((?!_next|favicon.ico).*)',
  ],
};