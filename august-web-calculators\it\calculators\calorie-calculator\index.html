<!DOCTYPE html>
<html lang="it">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Calcolatore del Fabbisogno Calorico Giornaliero - Calcolatore BMR & TDEE Preciso | MeetAugust</title>
    <meta name="description" content="Calcola il tuo fabbisogno calorico giornaliero con il nostro avanzato calcolatore BMR. Ottieni obiettivi calorici personalizzati per perdita di peso, mantenimento e aumento muscolare. Gratuito, preciso e basato sulla scienza." />
    <meta name="keywords" content="calcolatore calorie, calcolatore BMR, calcolatore TDEE, fabbisogno calorico giornaliero, calcolatore perdita di peso, calcolatore metabolismo, pianificazione nutrizionale" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="Calcolatore del Fabbisogno Calorico Giornaliero - Calcolatore BMR & TDEE Preciso" />
    <meta property="og:description" content="Calcola il tuo fabbisogno calorico giornaliero con il nostro avanzato calcolatore BMR. Ottieni obiettivi calorici personalizzati per perdita di peso, mantenimento e aumento muscolare." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Calcolatore del Fabbisogno Calorico Giornaliero - Calcolatore BMR & TDEE Preciso" />
    <meta name="twitter:description" content="Calcola il tuo fabbisogno calorico giornaliero con il nostro avanzato calcolatore BMR. Ottieni obiettivi calorici personalizzati per perdita di peso, mantenimento e aumento muscolare." />
    <link rel="canonical" href="https://www.meetaugust.ai/calculator/calorie" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/it/calculators/calorie-calculator" />
    <link rel="icon" href="/it/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/it/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img
            width="200"
            src="/it/calculators/assets/august_logo_green_nd4fn9.svg"
            alt="Logo Calcolatore"
          />
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select" style="border:1px solid #e5e7eb;border-radius:6px;padding:6px 8px;font-size:14px;color:#374151;background:#fff;outline:none;">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/redirect/wa?message=Hello%20August"
            class="talk-to-august"
            >Parla con August</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>Calcolatore Avanzato del Fabbisogno Calorico Giornaliero</h1>
        <p>Scopri quante calorie ti servono ogni giorno con il nostro calcolatore preciso. Perfetto per perdita di peso, aumento muscolare, <br /> o mantenere uno stile di vita sano in base al tuo corpo, obiettivi e livello di attività.</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">Unità USA</button>
            <button class="tab-button active" data-unit="metric">Unità Metriche</button>
          </div>
          <p class="form-instruction">Inserisci i tuoi dati personali qui sotto e clicca Calcola per ottenere le tue raccomandazioni caloriche personalizzate</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Età</label>
              <input type="text" id="age" value="25" />
              <span>età 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Genere</label>
              <input type="radio" name="gender" value="male" checked /> Maschio
              <input type="radio" name="gender" value="female" /> Femmina
            </div>
            <div class="form-field">
              <label for="height">Altezza</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>piedi</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>pollici</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">Peso</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>libbre</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">Attività</label>
              <select id="activity">
                <option value="sedentary">Sedentario: poco o nessun esercizio</option>
                <option value="light">Leggermente attivo: esercizio leggero 1-3 giorni/settimana</option>
                <option value="moderate" selected>Moderatamente attivo: esercizio moderato 3-5 giorni/settimana</option>
                <option value="very">Molto attivo: esercizio intenso 6-7 giorni/settimana</option>
                <option value="super">Super attivo: esercizio molto intenso, lavoro fisico</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ Impostazioni</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>Impostazioni</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>Unità risultati:</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">Calorie</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">Chilojoule</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Percentuale di Grasso Corporeo:
                  <span
                    class="info-icon"
                    title="Inserisci la percentuale di grasso corporeo per calcoli più accurati della composizione corporea"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Formula di stima BMR:
                  <span
                    class="info-icon"
                    title="Scegli la formula per calcolare il tuo Metabolismo Basale"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div |cutoff|>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">Harris-Benedict Rivisitata</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Calcola ▶
              </button>
              <button type="button" class="clear-button">Pulisci</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>Risultato</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="Scarica risultati in PDF"
            >
              <span class="download-icon">📥</span>
              <span>Scarica PDF</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">I tuoi obiettivi calorici personalizzati sono calcolati usando formule metaboliche avanzate. Queste raccomandazioni forniscono linee guida di assunzione calorica giornaliera su misura per i tuoi obiettivi specifici - che tu voglia mantenere il peso attuale, ottenere una perdita di peso sostenibile o supportare un aumento di peso sano.</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                Metabolismo Basale (BMR):
                <span id="bmr-value" style="color: #111827">1,650</span>
                calorie-calculator/giorno
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  Livelli di Attività:
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sedentario:</strong> poco o nessun esercizio
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Leggero:</strong> esercizio 1-3 volte/settimana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Moderato:</strong> esercizio 4-5 volte/settimana
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>Attivo:</strong> esercizio quotidiano o intenso 3-4 volte/settimana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Molto Attivo:</strong> esercizio intenso 6-7 volte/settimana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Extra Attivo:</strong> esercizio molto intenso ogni giorno, o lavoro fisico
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">Obiettivo</th>
                  <th style="width: 30%">Calorie</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="Obiettivo">
                    <div class="goal-label">Mantieni peso</div>
                  </td>
                  <td data-label="Calorie Giornaliere">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">calorie-calculator/giorno</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Obiettivo">
                    <div class="goal-label">Perdita di peso lieve</div>
                    <div class="goal-description">0.5 lb/settimana</div>
                  </td>
                  <td data-label="Calorie Giornaliere">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">calorie-calculator/giorno</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="Obiettivo">
                    <div class="goal-label">Perdita di peso</div>
                    <div class="goal-description">1 lb/settimana</div>
                  </td>
                  <td data-label="Calorie Giornaliere">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">calorie-calculator/giorno</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Obiettivo">
                    <div class="goal-label">Perdita di peso estrema</div>
                    <div class="goal-description">2 lb/settimana</div>
                  </td>
                  <td data-label="Calorie Giornaliere">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">calorie-calculator/giorno</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >Mostra info per aumento di peso</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                Informazioni su Aumento di Peso
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">Obiettivo</th>
                    <th style="width: 30%">Calorie</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Obiettivo">
                      <div class="goal-label">Aumento di peso lieve</div>
                      <div class="goal-description">0.25 kg/settimana</div>
                    </td>
                    <td data-label="Calorie Giornaliere">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">calorie-calculator/giorno</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="Obiettivo">
                      <div class="goal-label">Aumento di peso</div>
                      <div class="goal-description">0.5 kg/settimana</div>
                    </td>
                    <td data-label="Calorie Giornaliere">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">calorie-calculator/giorno</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Obiettivo">
                      <div class="goal-label">Aumento di peso veloce</div>
                      <div class="goal-description">1 kg/settimana</div>
                    </td>
                    <td data-label="Calorie Giornaliere">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">calorie-calculator/giorno</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>Linee Guida Attività Fisica</h2>
          <ul>
            <li>
              <strong>Esercizio Leggero:</strong> 20-40 minuti di attività a intensità moderata come camminare o yoga leggero.
            </li>
            <li>
              <strong>Esercizio Moderato:</strong> 30-60 minuti di attività che aumentano la frequenza cardiaca, come camminata veloce o ciclismo.
            </li>
            <li>
              <strong>Esercizio Intenso:</strong> 45-90 minuti di allenamento ad alta intensità, sport o attività fisiche impegnative.
            </li>
            <li>
              <strong>Allenamento Professionale/Atletico:</strong> 2+ ore di allenamento intensivo o lavoro occupazionale fisicamente impegnativo.
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>Guida Nutrizionale Completa di Riferimento</h2>
          <p>Usa queste tabelle complete per fare scelte dietetiche informate e comprendere meglio il contenuto calorico degli alimenti quotidiani, strategie di pianificazione dei pasti e dispendio energetico dell'esercizio.</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Contenuto Calorico degli Alimenti Popolari
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Alimento
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Porzione
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Calorie
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Frutta Fresca
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mango
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 medio (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kiwi
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 grande (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mirtilli
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Avocado
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 medio (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Ciliegie
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Verdure Fresche
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Patata dolce
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 media (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Peperone
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza a fette
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Spinaci
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2 tazze fresche
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Zucchina
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza a fette
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Cavolfiore
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Fonti di Proteine
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Salmone, grigliato
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Petto di tacchino
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Yogurt greco
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Vasetto da 6 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mandorle
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 oz. (23 noci)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lenticchie, cotte
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Cereali & Amidi
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Quinoa, cotta
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Avena
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza cotta
                    </td>
                    <td style="padding: 8/*cutoff*/px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pasta integrale
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza cotta
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Riso integrale
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza cotta
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Bevande
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Tè verde
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Latte di mandorla
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Acqua di cocco
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 tazza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Vino rosso
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              Nota: I valori calorici sono approssimativi e possono variare in base ai metodi di preparazione e alle marche specifiche.
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Pianificazione Strategica dei Pasti
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Periodo del Pasto
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Piano da 1.300 Calorie
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Piano da 1.600 Calorie
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Piano da 2.100 Calorie
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Colazione
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 tazza di yogurt greco con 1/2 tazza di mirtilli (130 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tazza di avena con 1/2 tazza di mirtilli (238 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tazza di avena con 1 kiwi, 1 oz. di mandorle (458 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Spuntino Mattutino
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 kiwi piccolo (56 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mango medio (135 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mango medio, 10 ciliegie (232 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Totale Mattina
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      186 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      373 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      690 cal
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Pranzo
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      2 tazze di insalata di spinaci con 3 oz. di salmone grigliato (189 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. di petto di tacchino, 1 tazza di zucchina, 1/2 tazza di quinoa (264 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. di salmone grigliato, 1 tazza di riso integrale, 1 tazza di cavolfiore (418 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Spuntino Pomeridiano
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tazza di peperone a fette (28 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 avocado (160 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 avocado, 1 oz. di mandorle (324 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Totale Mezzogiorno
                    </td>
                    <td
.PerformAction("cutoff") style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      217 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      424 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      742 cal
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Cena
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. di petto di tacchino, 1 tazza di cavolfiore (150 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. di salmone grigliato, 1 tazza di patata dolce (287 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. di petto di tacchino, 1 tazza di pasta integrale, 1 tazza di spinaci (313 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Spuntino Sera
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tazza di tè verde (2 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tazza di acqua di cocco (46 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 tazza di yogurt greco (130 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Totale Sera
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      152 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      333 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      443 cal
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Totale Giornaliero
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1.255 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1.630 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2.175 cal
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>Padroneggia il Tuo Metabolismo</h2>
          <p>Comprendere il tuo metabolismo è fondamentale per raggiungere i tuoi obiettivi di salute. Questo calcolatore utilizza formule scientificamente validate per stimare il tuo Metabolismo Basale (BMR) e il Dispendio Energetico Totale Giornaliero (TDEE).</p>

          <div class="equations-container">
            <h3>Tre Formule Provate per il BMR</h3>
            <p>Questo calcolatore utilizza tre equazioni ben studiate per stimare il tuo BMR, ognuna con punti di forza unici a seconda del tuo profilo:</p>

            <div class="equation-card">
              <h4>Equazione di Mifflin-St Jeor</h4>
              <p>
                <strong>Maschio:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>Femmina:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                Considerata la più precisa per la popolazione generale, soprattutto per i non atleti.
              </p>
            </div>

            <div class="equation-card">
              <h4>Equazione Harris-Benedict Rivisitata</h4>
              <p>
                <strong>Maschio:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>Femmina:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                Una formula affidabile aggiornata per la precisione moderna, adatta a una vasta gamma di individui.
              </p>
            </div>

            <div class="equation-card">
              <h4>Formula Katch-McArdle</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                Ideale per chi conosce la propria percentuale di grasso corporeo, poiché tiene conto della massa magra.
              </p>
            </div>

            <div class="info-text">
              <h3>TDEE: Trasformare il BMR in Obiettivi Concreti</h3>
              <p>Il tuo Dispendio Energetico Totale Giornaliero (TDEE) è il tuo BMR moltiplicato per un fattore di attività che riflette il tuo stile di vita. Questo ti dà un quadro completo delle tue esigenze caloriche giornaliere.</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Livello di Attività
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Descrizione
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Moltiplicatore
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sedentario
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        poco o nessun esercizio
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Leggermente Attivo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        esercizio 1-3 volte/settimana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Moderatamente Attivo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        esercizio 4-5 volte/settimana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Molto Attivo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        esercizio intenso 6-7 volte/settimana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Super Attivo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        esercizio molto intenso ogni giorno, o lavoro fisico
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>Il risultato è una stima precisa delle calorie che bruci ogni giorno, che puoi usare per adattare la tua dieta per perdita di peso, mantenimento o aumento.</p>

              <h3>Gestione Strategica del Peso</h3>
              <p>Per perdere peso serve un deficit calorico; per aumentare peso, un surplus. Questo calcolatore fornisce obiettivi calorici precisi per ogni scopo, garantendo progressi sostenibili.</p>

              <div class="warning-note">
                <strong>Linee Guida Critiche per una Perdita di Peso Sicura:</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Evita deficit estremi per non rallentare il metabolismo.</li>
                  <li>Assicurati un'adeguata assunzione di proteine per preservare la massa muscolare.</li>
                  <li>Dai priorità ad alimenti ricchi di nutrienti per evitare carenze.</li>
                  <li>Evita diete drastiche per prevenire il recupero del peso.</li>
                </ul>
                <p style="margin-top: 12px">Per risultati ottimali, consulta un nutrizionista o dietista per personalizzare ulteriormente il tuo piano.</p>
              </div>

              <h3>Strategie di Ottimizzazione Nutrizionale</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>Dai priorità ad alimenti ricchi di nutrienti come verdure, proteine magre e cereali integrali.</li>
                <li>Assicurati un'adeguata assunzione di proteine per supportare la massa muscolare e la sazietà.</li>
                <li>Rifiuta diete troppo restrittive a favore di un'alimentazione sostenibile ed equilibrata.</li>
                <li>Monitora costantemente l'assunzione per costruire abitudini sane a lungo termine.</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                Questo calcolatore è un punto di partenza. Adatta in base ai tuoi progressi e consulta professionisti per una guida personalizzata.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>Monitoraggio Calorico di Precisione</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>Passo 1: Determina la Tua Base Metabolica</h4>
              <p>Usa questo calcolatore per trovare il tuo BMR e TDEE in base a età, genere, peso, altezza e livello di attività.</p>
            </div>

            <div class="step-card">
              <h4>Passo 2: Stabilisci i Tuoi Obiettivi di Peso</h4>
              <p>Stabilisci obiettivi realistici per perdita di peso, mantenimento o aumento, usando le raccomandazioni caloriche fornite.</p>
            </div>

            <div class="step-card">
              <h4>Passo 3: Implementa Sistemi di Monitoraggio</h4>
              <p>Monitora l'assunzione calorica con app o un diario alimentare per restare allineato agli obiettivi.</p>
              <p>Pesati settimanalmente e monitora le tendenze, non le fluttuazioni giornaliere.</p>
            </div>

            <div class="step-card">
              <h4>Passo 4: Ottimizza Attraverso la Valutazione</h4>
              <p>Rivaluta il fabbisogno calorico ogni 4-6 settimane o dopo cambiamenti di peso significativi per mantenere efficace il piano.</p>
            </div>
          </div>

          <div class="info-text">
            <h3>La Scienza dell'Equilibrio Calorico</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>Fondamenti dell'Equilibrio Energetico:</strong> La gestione del peso è governata dalle calorie in entrata rispetto a quelle in uscita.</li>
              <li><strong>Effetto Termico degli Alimenti:</strong> Alimenti diversi richiedono quantità diverse di energia per essere digeriti, influenzando il totale delle calorie bruciate.</li>
              <li><strong>Sazietà e Qualità degli Alimenti:</strong> Alimenti ricchi di fibre e proteine favoriscono la sazietà, aiutandoti a seguire il piano.</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              Esempio: La 'Dieta Twinkie' ha dimostrato che la perdita di peso è possibile con un deficit calorico, ma diete povere di nutrienti danneggiano la salute a lungo termine.
            </p>

            <h4 style="color: #416955; margin-top: 20px">Benefici Extra del Monitoraggio Calorico</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Costruisce consapevolezza nutrizionale e abitudini alimentari consapevoli.</li>
              <li>Migliora il controllo delle porzioni tramite monitoraggio costante.</li>
              <li>Collega le scelte alimentari all'esercizio per ottimizzare l'equilibrio energetico.</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Ciclismo Calorico Zigzag</h2>
          <p>Il ciclismo calorico zigzag comporta la variazione dell'apporto calorico giornaliero mantenendo l'obiettivo settimanale per migliorare la flessibilità metabolica e prevenire i plateau.</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>Esempio</h4>
              <p><strong>Obiettivo Settimanale:</strong> 14.000 calorie (2.000 calorie-calculator/giorno in media)</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>Opzione A:</strong> 7 giorni a 2.000 calorie.</li>
                <li><strong>Opzione B:</strong> 5 giorni a 1.800 calorie, 2 giorni a 2.500 calorie.</li>
              </ul>
              <p>Entrambe le opzioni raggiungono l'obiettivo settimanale ma variano l'apporto giornaliero per mantenere il metabolismo dinamico.</p>
            </div>

            <div class="benefits-card">
              <h4>Obiettivo Zigzag</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>Migliore flessibilità metabolica e aderenza.</li>
                <li>Maggiore flessibilità nella pianificazione dei pasti, soprattutto per eventi sociali.</li>
                <li>Previene l'adattamento metabolico da deficit prolungati.</li>
                <li>Supera i plateau di perdita di peso variando l'apporto calorico.</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>Fabbisogno Calorico per Stile di Vita</h2>
          <p>Il fabbisogno calorico varia in base a fattori individuali, ma le linee guida generali possono aiutarti a iniziare.</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Fattori che influenzano il fabbisogno calorico</h4>
              <ul>
                <li>Età, sesso, peso e altezza.</li>
                <li>Livello di attività (sedentario ad altamente attivo).</li>
                <li>Stato di salute, inclusa gravidanza o condizioni mediche.</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>Linee Guida Generali</h4>
              <p><strong>Uomini:</strong> 2.000–3.000 calorie-calculator/giorno</p>
              <p><strong>Donne:</strong> 1.600–2.400 calorie-calculator/giorno</p>
            </div>

            <div class="minimum-card">
              <h4>Assunzione Minima Sicura</h4>
              <p><strong>Donne:</strong> 1.200 calorie-calculator/giorno</p>
              <p><strong>Uomini:</strong> 1.500 calorie-calculator/giorno</p>
              <p class="warning">Assunzioni inferiori a questi livelli devono essere supervisionate da un medico.</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>Restringere troppo le calorie può portare a carenze nutrizionali, perdita muscolare e rallentamento del metabolismo. Dai sempre priorità alla salute.</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>Non Tutte le Calorie Sono Uguali</h2>
          <p>Le calorie provengono da diversi macronutrienti, ognuno con effetti unici sul corpo:</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>Proteine: 4 calorie-calculator/grammo – supportano la riparazione muscolare e la sazietà.</li>
            <li>Carboidrati: 4 calorie-calculator/grammo – fonte primaria di energia.</li>
            <li>Grassi: 9 calorie-calculator/grammo – essenziali per ormoni e assorbimento dei nutrienti.</li>
            <li>Alcol: 7 calorie-calculator/grammo – valore nutrizionale minimo.</li>
          </ul>

          <p>Le etichette nutrizionali forniscono conteggi calorici accurati, ma le porzioni e i metodi di preparazione contano.</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>Alimenti ad Alto Contenuto Calorico</h4>
              <p>Densi di calorie, spesso a causa di grassi o zuccheri. Usare con moderazione per la gestione del peso.</p>
              <ul>
                <li>Avocado, oli.</li>
                <li>Noci e semi.</li>
                <li>Cibi fritti.</li>
                <li>Dolci e snack zuccherati.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Alimenti a Basso Contenuto Calorico</h4>
              <ul>
                <li>Molte verdure (es. spinaci, zucchine).</li>
                <li>Alcuni frutti (es. frutti di bosco).</li>
                <li>Proteine magre (es. tacchino, pesce).</li>
                <li>Cereali integrali con moderazione.</li>
                <li>Verdure a foglia verde per volume e nutrienti.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Calorie Vuote</h4>
              <ul>
                <li>Bevande zuccherate (es. bibite).</li>
                <li>Snack lavorati (es. patatine, biscotti).</li>
                <li>Zuccheri aggiunti negli alimenti confezionati.</li>
                <li>Grassi solidi (es. burro, margarina).</li>
                <li>Alcol con beneficio nutrizionale minimo.</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>Perché Conta la Qualità Calorica</h3>
            <p>Bevande come bibite o alcol aggiungono calorie senza sazietà, rendendo più difficile mantenere un deficit.</p>

            <h4 style="color: #416955; margin-top: 16px">Crea un Piano Bilanciato</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Concentrati su alimenti integrali e non processati per la densità nutrizionale.</li>
              <li>Limita snack e bevande zuccherate.</li>
              <li>Usa alimenti ricchi di fibre e proteine per un controllo naturale delle porzioni.</li>
              <li>Combina il conteggio delle calorie con l'esercizio per risultati sostenibili.</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>Conclusione Finale</h2>
          <p>Non esiste un approccio unico per la nutrizione. Usa questo calcolatore come punto di partenza, monitora i tuoi progressi e adatta secondo le tue esigenze uniche.</p>
          <p style="font-weight: 500; color: #416955">Monitora con saggezza, mangia consapevolmente e dai priorità alla salute a lungo termine.</p>
        </section>
      </main>
    </div>
    <script src="/it/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>