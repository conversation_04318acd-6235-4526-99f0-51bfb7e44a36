'use client';
import { createContext, useContext, useState, useEffect } from 'react';
import { languages, defaultLanguage } from '../lib/i18n/config';
const logger = require('../utils/logger');

// Dynamic import function for translations
const loadTranslation = async (language) => {
  try {
    const module = await import(`../lib/i18n/translations/${language}.js`);
    return module[language] || module.default || {};
  } catch (error) {
    logger.warn(`Failed to load translation for ${language}, falling back to English`);
    // Fallback to English if translation fails to load
    const fallback = await import('../lib/i18n/translations/en.js');
    return fallback.en || fallback.default || {};
  }
};

// Cache for loaded translations
const translationCache = new Map();

const LanguageContext = createContext();

export function LanguageProvider({ children, initialLanguage }) {
  const [language, setLanguage] = useState(initialLanguage || defaultLanguage);
  const [translations, setTranslations] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  // Load translation for current language
  useEffect(() => {
    const loadLanguageTranslations = async () => {
      if (translationCache.has(language)) {
        setTranslations(translationCache.get(language));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const translation = await loadTranslation(language);
        translationCache.set(language, translation);
        setTranslations(translation);
      } catch (error) {
        logger.error('Failed to load translations:', error);
        // Use empty object as fallback
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguageTranslations();
  }, [language]);

  const t = (key) => {
    if (isLoading || !translations) {
      return key; // Return key as fallback while loading
    }
    
    const keys = key.split('.');
    let value = translations;
    for (const k of keys) {
      value = value?.[k];
    }
    return value || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
}

export const useLanguage = () => useContext(LanguageContext);
